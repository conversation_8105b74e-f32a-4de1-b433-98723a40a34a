<script setup lang="ts">
const model = defineModel()

const edit = ref(false)

const divRef = ref(null)
const inputRef = ref(null)
function editable(e) {
  inputRef.value.focus()
  edit.value = true
}
function blurInput() {
  edit.value = false
}
function checkBlur(e) {
  if (!e.target.contains(divRef.value)) {
    edit.value = false
  }
}
window.addEventListener('click', checkBlur)
onUnmounted(() => {
  window.removeEventListener('click', checkBlur)
})
</script>

<template>
  <div ref="divRef" class="inline-block h-fit w-fit relative cursor-text" @click="editable">
    <el-input v-show="edit" ref="inputRef" v-model="model" style="width: 100%;height: 100%;" @blur="blurInput" />
    <span v-show="!edit" style="width:100%;pointer-events: none;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;" class="text-[rgba(0,0,0,0.85)] w-fit">{{ model }}</span>
  </div>
</template>

<style scoped>
:deep(.el-input__wrapper) {
  box-shadow: none;
  background: #eaf9f9;
}
</style>