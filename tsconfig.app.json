{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "jsx": "preserve",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "moduleDetection": "force",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "baseUrl": ".",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,

    /* Linting */
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    "skipLibCheck": true,
    "isolatedModules": true,
    "noUncheckedSideEffectImports": true,
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
  },

  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue","site/**/*.ts","packages/**/*.ts"]
}
