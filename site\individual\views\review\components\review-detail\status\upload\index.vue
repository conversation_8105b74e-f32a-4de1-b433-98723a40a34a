<script setup lang="ts">
import UploadPlaceholder from '@comp/src/file-suffix-uploader/upload-placeholder.vue'
import { cn } from '@paper-review/utils'
import { computed, ref } from 'vue'

interface FileTypeConfig {
  mimeTypes: string[]
  extensions: string[]
  icon: string
  label: string
}

interface Props {
  allowedTypes?: string[]
  customFileTypes?: Record<
    string,
    Omit<FileTypeConfig, 'icon' | 'label'> &
    Partial<Pick<FileTypeConfig, 'icon' | 'label'>>
  >
  disabled?: boolean
  class?: string
  onExceed: () => void
}

interface Emits {
  (e: 'fileSelected', file: File): void
  (e: 'fileRejected', file: File, reason: string): void
  (e: 'dragEnter'): void
  (e: 'dragLeave'): void
}

const props = withDefaults(defineProps<Props>(), {
  allowedTypes: () => ['docx', 'pdf', 'zip'],
  customFileTypes: () => ({}),
  disabled: false,
  onExceed: () => {},
})

const emit = defineEmits<Emits>()

const fileInputRef = ref<HTMLInputElement>()
const isDragOver = ref(false)
const currentVariant = ref<string>('default')

// 内置文件类型配置
const defaultFileTypeConfig: Record<string, FileTypeConfig> = {
  docx: {
    mimeTypes: [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
    extensions: ['.docx'],
    icon: '📄',
    label: 'DOCX 文档',
  },
  pdf: {
    mimeTypes: ['application/pdf'],
    extensions: ['.pdf'],
    icon: '📕',
    label: 'PDF 文档',
  },
  zip: {
    mimeTypes: ['application/zip', 'application/x-zip-compressed'],
    extensions: ['.zip'],
    icon: '🗜️',
    label: 'ZIP 压缩包',
  },
}

// 合并内置配置和自定义配置
const fileTypeConfig = computed(() => {
  const merged = { ...defaultFileTypeConfig }

  // 合并自定义文件类型配置
  Object.entries(props.customFileTypes).forEach(([type, config]) => {
    merged[type] = {
      mimeTypes: config.mimeTypes,
      extensions: config.extensions,
      icon: config.icon || '📄',
      label: config.label || type.toUpperCase(),
    }
  })

  return merged
})

const acceptedFileTypes = computed(() => {
  return props.allowedTypes
    .map((type) => {
      const config = fileTypeConfig.value[type]
      if (!config) 
        return ''
      return [...config.mimeTypes, ...config.extensions].join(',')
    })
    .filter(Boolean)
    .join(',')
})

function getFileType(file: File): string {
  const extension = `.${file.name.split('.').pop()?.toLowerCase() || ''}`

  for (const [type, config] of Object.entries(fileTypeConfig.value)) {
    // 检查 MIME 类型
    if (config.mimeTypes.includes(file.type)) {
      return type
    }
    // 检查文件扩展名
    if (config.extensions.includes(extension)) {
      return type
    }
  }

  return 'unknown'
}

function isFileAllowed(file: File): boolean {
  const fileType = getFileType(file)
  return props.allowedTypes.includes(fileType)
}

function processFile(file: File) {
  if (isFileAllowed(file)) {
    emit('fileSelected', file)
  }
  else {
    emit('fileRejected', file, '不支持的文件类型')
  }
}

function handleClick() {
  if (!props.disabled) {
    fileInputRef.value?.click()
  }
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    processFile(file)
  }

  // 重置 input
  target.value = ''
}

function handleDragEnter(event: DragEvent) {
  event.preventDefault()

  if (!props.disabled) {
    isDragOver.value = true
    emit('dragEnter')

    // 检查拖拽的文件类型
    const items = event.dataTransfer?.items

    if (items && items.length > 0) {
      const item = items[0]

      if (item.kind === 'file') {
        // 使用 MIME 类型检测
        const mimeType = item.type
        let fileType = 'unknown'

        // 根据 MIME 类型判断文件类型
        for (const [type, config] of Object.entries(fileTypeConfig.value)) {
          if (config.mimeTypes.includes(mimeType)) {
            fileType = type
            break
          }
        }

        // 如果 MIME 类型检测失败，尝试从文件名检测（如果可用）
        if (fileType === 'unknown') {
          try {
            const file = item.getAsFile()
            if (file && file.name) {
              const extension = `.${
                file.name.split('.').pop()?.toLowerCase() || ''
              }`

              for (const [type, config] of Object.entries(
                fileTypeConfig.value,
              )) {
                if (config.extensions.includes(extension)) {
                  fileType = type
                  break
                }
              }
            }
          }
          catch (e) {
            // 忽略错误，继续使用 unknown
          }
        }

        if (fileType !== 'unknown' && props.allowedTypes.includes(fileType)) {
          currentVariant.value = fileType
        }
        else {
          currentVariant.value = 'forbidden'
        }
      }
    }
  }
}

function handleDragOver(event: DragEvent) {
  event.preventDefault()
  // 保持拖拽状态，不需要额外逻辑
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault()
  // 检查是否真的离开了组件区域
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false
    currentVariant.value = 'default'
    emit('dragLeave')
  }
}

function handleDrop(event: DragEvent) {
  event.preventDefault()
  isDragOver.value = false
  currentVariant.value = 'default'

  if (event.dataTransfer?.files && event.dataTransfer?.files.length > 1) {
    props.onExceed()
    return
  }

  if (
    !props.disabled
    && event.dataTransfer?.files
    && event.dataTransfer?.files.length === 1
  ) {
    const file = event.dataTransfer?.files[0]
    if (file) {
      processFile(file)
    }
  }
}

// 辅助函数
function getFileTypeConfig(variant: string) {
  if (variant === 'forbidden') {
    return {
      icon: '❌',
      label: '不支持的格式',
    }
  }
  return fileTypeConfig.value[variant]
}

// 暴露组件引用
defineExpose({
  UploadPlaceholder,
})
</script>

<template>
  <div
    :class="
      cn(
        'relative w-64 h-48 border border-dashed rounded-lg cursor-pointer transition-all duration-200',
        {
          'opacity-50 cursor-not-allowed': disabled,
          'scale-105': isDragOver && !disabled,
        },
        'bg-[#fff]',
        props.class,
      )
    "
    @click="handleClick"
    @drop="handleDrop"
    @dragenter="handleDragEnter"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
  >
    <input
      ref="fileInputRef"
      type="file"
      :accept="acceptedFileTypes"
      :disabled="disabled"
      class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
      @change="handleFileChange"
    >

    <!-- 默认内容 slot -->
    <div
      v-if="currentVariant === 'default'"
      class="absolute inset-0 flex items-center justify-center"
    >
      <slot name="default">
        <UploadPlaceholder :allowed-types="allowedTypes" />
      </slot>
    </div>

    <!-- 动态文件类型 slot -->
    <div v-else class="absolute inset-0 flex items-center justify-center">
      <slot
        :name="`${currentVariant}-content`"
        :variant="currentVariant"
        :file-type="currentVariant"
        :config="getFileTypeConfig(currentVariant)"
      >
        <!-- 默认的文件类型显示 -->
        <div class="text-center">
          <div class="text-2xl text-gray-600">
            {{ getFileTypeConfig(currentVariant)?.icon || "📄" }}
          </div>
          <div class="text-xs mt-1 text-gray-700">
            {{
              getFileTypeConfig(currentVariant)?.label ||
                currentVariant.toUpperCase()
            }}
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>
