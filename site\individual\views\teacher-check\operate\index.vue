<!-- eslint-disable no-console -->
<script setup>
import { getPaperReviewRecordsApi } from '../../../service/api'
import ListHeader from '../components/ListHeader.vue'
import Table from '../components/Table.vue'
import Title from '../components/Title.vue'
import CheckOperateForm from './components/CheckOperateForm.vue'
import CheckOperateResult from './components/CheckOperateResult.vue'
import PaperInfo from './components/PaperInfo.vue'
import PathBlurCard from './components/PathBlurCard.vue'

const responseData = ref({})
const pageLoading = ref(false)

const params = useUrlSearchParams('hash')

async function getPaperReviewRecords() {
  pageLoading.value = true
  getPaperReviewRecordsApi({
    code: params?.code,
  }).then((res) => {
    responseData.value = res.data.data
  }).catch((error) => {
    console.error('获取的结果===>', error)
  }).finally(() => {
    pageLoading.value = false
  })
}
provide('reportDetail', responseData)

onMounted(() => {
  getPaperReviewRecords()
})

function handleSubmitFinish() {
  getPaperReviewRecords()
}
</script>

<template>
  <div v-loading="pageLoading" class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm p-8">
      <!-- Title Section -->
      <Title 
        :title="`${responseData?.author || '暂无'}《${responseData?.titleCn || '暂无'}》`"
        :time="responseData?.createTime || '暂无'"
      />

      <!-- Paper Information Section -->
      <div class="mb-8">
        <ListHeader />
        <PaperInfo
          :word-count="responseData?.result?.wordCount"
          :format-error="responseData?.result?.formatError"
          :semantic-deviation="responseData?.result?.semanticDeviation"
          :improvement-suggestions="responseData?.result?.improvementSuggestions"
        />
      </div>

      <!-- Test Report Section -->
      <div class="mb-8">
        <ListHeader
          chinese="检测报告"
          english="TEST REPORT"
        />

        <PathBlurCard 
          :title="responseData?.titleCn"
          :author="responseData?.author"
          :university="responseData?.school"
          :degree="responseData?.degree"
          :preview-url="responseData?.result?.previewUrl"
          :download-url="responseData?.result?.downloadUrl"
        />
        
        <Table
          type="history"
          :table-data="responseData?.records"
        >
          <template #header>
            <div class="w-full h-full flex items-center ">
              <div 
                class="
                  flex flex-row items-center gap-4
                  flex-shrink-0 
                "
              >
                <span class="text-[16px] !text-black ">历史检测记录</span>

                <div>
                  <span class="text-sm font-normal text-gray-500">系统共检测</span>
                  <span class="text-sm font-normal text-red-500">{{ responseData?.reviewCount || 0 }}</span>
                  <span class="text-sm font-normal text-gray-500">次 </span>
                </div>
                <!-- 
                <div>
                  <span class="text-sm font-normal text-gray-500">发现有效问题</span>
                  <span class="text-sm font-normal text-red-500">{{ responseData?.validQuestionCount || 0 }}</span>
                  <span class="text-sm font-normal text-gray-500">处</span>
                </div> -->
              </div>
            </div>
          </template>
        </Table>
      </div>

      <!-- Review Operations Section -->
      <div class="mb-8">
        <ListHeader
          chinese="审核操作"
          english="REVIEW OPERATIONS"
        />
        
        <!-- 0.未提交 1.审核中 2.不通过 3.初步审核通过 4.最终审核通过 -->
        <CheckOperateForm 
          v-if="responseData?.approvalStatus === '1'"
          :parent-data="responseData"
          @submit-finish="handleSubmitFinish"
        />

        <CheckOperateResult 
          v-else-if="responseData?.approvalStatus === '2'"
          :status="false"
          :review-reason="responseData?.reason"
          :person="responseData?.teacherName"
          :time="responseData?.reviewTime"
        />

        <CheckOperateResult 
          v-else-if="responseData?.approvalStatus === '3'"
          :status="true"
          :review-reason="responseData?.reason"
          :person="responseData?.teacherName"
          :time="responseData?.reviewTime"
        />
      </div>
    </div>
  </div>
</template>