<script setup lang="ts">
import { Lock, Message, View } from '@element-plus/icons-vue'

const ruleForm = reactive({
  account: '',
  passWord: '',
  readed: false,
})

const passwordVisable = ref(false)
function switchVisable() {
  passwordVisable.value = !passwordVisable.value 
}
function cleanRuleForm() {
  ruleForm.account = ''
  ruleForm.passWord = ''
  ruleForm.readed = false
}
onUnmounted(() => {
  cleanRuleForm()
})
</script>

<template>
  <div class="w-full flex pl-[10.6rem] mt-[6rem]">
    <el-form class="w-[37.2rem]"> 
      <el-form-item prop="pass">
        <el-input v-model="ruleForm.account" :prefix-icon="Message" autocomplete="off" class="h-[52px]" placeholder="邮箱或手机号" />
      </el-form-item>
      <el-form-item prop="pass">
        <el-input v-model="ruleForm.passWord" :prefix-icon="Lock" type="password" autocomplete="off" class="h-[52px]" placeholder="密码">
          <template #suffix>
            <div class="cursor-pointer h-full flex items-center" @click="switchVisable">
              <svg v-show="passwordVisable" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M22 8s-4 6-10 6S2 8 2 8m13 5.5l1.5 2.5m3.5-5l2 2M2 13l2-2m5 2.5L7.5 16" color="currentColor" /></svg>
              <el-icon v-show="!passwordVisable">
                <View />
              </el-icon> 
            </div>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="type" class="relative">
        <div class="w-full">
          <el-checkbox v-model="ruleForm.readed" name="type">
            <span class="text-[rgba(37,43,58,0.85)] text-[12px]">我已阅读并同意<span class="text-[#06B190]">服务条款、隐私政策</span></span>
          </el-checkbox> 
          <span class="text-[#06B190] text-[12px] float-right">忘记密码</span>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" class="box-border w-full p-0 m-0" style="background: linear-gradient(90deg, #6ED86E, #1DB989);border: none;height: 50px;">
          登录
        </el-button>
      </el-form-item>
      <el-form-item>
        <div class="w-full">
          <span class="text-[#06B190] float-right cursor-pointer">没有账号？去注册</span>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>

</style>