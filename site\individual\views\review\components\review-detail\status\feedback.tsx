import { CloseBold, Select } from '@element-plus/icons-vue'
import { css } from '@emotion/css'
import clockIcon from '@individual/assets/svg/clock.svg'
import loadingSvg from '@individual/assets/svg/loading.svg'
import failStatus from '@individual/assets/svg/status-fail.svg'
import finishedStatus from '@individual/assets/svg/status-ok.svg'
import waitStatus from '@individual/assets/svg/status-wait.svg'
import { FileType } from '@paper-review/components'
import { getFileSize } from '@paper-review/utils'
import day from 'dayjs'
import { ElMessage } from 'element-plus'
import { paperDownload } from '../../../../../service/api/sys'

const spinnerCss = css`
  & {
  .circular{
      width:32px;
      height:32px;
      position:absolute;
      left:-23px;
      bottom:-31px;
    }
  }
`
const checkStatusMapBar = {
  CHECKING: () => (
    <div class="w-full h-[52px]  flex items-center border-t-[3px] px-[30px] py-[13px] bg-[#f1f4fe] border-[#5478ee]">
      <div class="text-[20px] text-[#5478ee] mr-[24px]">检测中</div>
      <div class="text-[20px] mr-[24px]">
        已发现错误
        <span class="text-[#d10000]">50</span>
        处
      </div>
    </div>
  ),
  QUEUING: () => (
    <div class="w-full h-[52px]  flex items-center border-t-[3px] px-[30px] py-[13px] bg-[#fff7ef] border-[#ffaa00]">
      <div class="text-[20px] text-[#ffaa00] mr-[24px]">排队等待中</div>
    </div>
  ),
  FAILURE: () => (
    <div class="w-full h-[52px] flex items-center border-t-[3px] px-[30px] py-[13px] bg-[#fcf0f0] border-[#ff4d4f]">
      <div class="text-[20px] text-[#ff4d4f] mr-[24px]">检测失败</div>
      <div class="text-[20px] mr-[24px]">
        任务失败原因：
        <span class="text-[#ff4d4f] text-[20px]">网络错误</span>
      </div>
    </div>
  ),
  SUCCESS: () => (
    <div class="w-full h-[52px] flex flex-wrap items-center bg-[#eaf9f9] py-[13px] relative">
      <div class="h-[3px] w-full absolute top-0" style="background: linear-gradient(90deg,#82df67, #06b190);"></div>
      <div class="text-[20px] text-[#06b190] mr-[24px] pl-[30px]">检测完成</div>
      <div class="text-[20px] mr-[24px]">
        共发现错误
        <span class="text-[#d10000]">50</span>
        处
      </div>
    </div>
  ),
}
const checkStatusMapTip = {
  CHECKING: () => (
    <div class="flex flex-nowrap items-center">
      <img src={clockIcon as any} class="w-[16px] h-[16px] object-contain mr-[8px]" />
      <span class="text-[16px] mr-[10px]">检测中</span>
      <span class="text-[16px] text-[rgba(0,0,0,0.45)]">预估还需：58秒</span>
    </div>
  ),
  QUEUING: () => (
    <div class="flex flex-nowrap items-center">
      <img src={loadingSvg as any} class="w-[16px] h-[16px] object-contain mr-[8px] animate-spin" />
      <span class="text-[16px] mr-[10px]">
        服务器正在为您努力争取资源中，请稍候片刻
      </span>
    </div>
  ),
  FAILURE: () => (
    <div class="flex flex-nowrap items-center">
      <CloseBold class="w-[16px] h-[16px] mr-[8px]"></CloseBold>
      <span class="text-[16px] mr-[10px]">论文检测失败，本次消耗的检测次数已退还，请重新检测</span>
    </div>
  ),
  SUCCESS: () => (
    <div class="flex flex-nowrap items-center">
      <Select class="w-[16px] h-[16px] mr-[8px]"></Select>
      <span class="text-[16px] mr-[10px]">论文检测已完成</span>
    </div>
  ),
}
const statusIconMap = { QUEUING: waitStatus, FAILURE: failStatus, SUCCESS: finishedStatus }

export default defineComponent({
  props: { fileType: String, status: Number },
  setup() {
    const STATUS = inject('STATUS') as any
    const { status, setStatus } = inject('useStatus') as any
    const { frontTempFileInfo } = inject('useFrontTempFileInfo') as any
    const progress = css`
      & .el-progress-bar__inner {
        background-image: linear-gradient(90deg, #81df68 0%, #14b68c 100%);;
      }
    `

    const getSTATUS = (value): string => {
      if (value === '' || value === null || value === undefined) 
        return STATUS.QUEUING
      // eslint-disable-next-line unicorn/prefer-number-properties
      if (typeof value == 'string' && isNaN(value as any)) 
        return STATUS[STATUS[value]]
      else
        return STATUS[Number.parseInt(value)]
    }

    const activeStatus = ref(getSTATUS(status.value))
    const paperUrl = ref('')

    let eventSource = null as any
    const cleanEventSource = () => {
      eventSource.close && eventSource.close()
      eventSource = Object.create(null)
    }

    const sseConnect = () => {
      if (!eventSource) {
        eventSource = new EventSource(`/api/review/streamData?taskId=${frontTempFileInfo.value.reviewId}`)
        eventSource.onmessage = function (event) {
          const data = JSON.parse(event.data)
          paperUrl.value = data.wordUrl
          activeStatus.value = getSTATUS(data.status) 

          if (![getSTATUS(STATUS.CHECKING), getSTATUS(STATUS.QUEUING)].includes(getSTATUS(activeStatus.value))) {
            queueMicrotask(() => {
              cleanEventSource()
            })
          }
        }
      }
    }
    sseConnect()

    const reCheck = () => {
      cleanEventSource()
      setStatus(STATUS.ORIGINALSTATUS)
    }

    const downloadPaper = async () => {
      try {
        const res = await paperDownload({
          fileUrls: [paperUrl.value].toString(),
        })
        if (res.response.data.status !== 200) {
          ElMessage({
            message: '文件下载失败，请稍后重试',
            type: 'error',
          })
          return 
        }
        const date = day().format('YYYYMMDD')
        const blobObj = new Blob([res.data], { type: 'application/zip' })
        const url = window.URL.createObjectURL(blobObj)
        const a = document.createElement('a')
        a.href = url
        a.download = `论文检测系统_${date}.zip` 
        document.body.appendChild(a)
        a.click()
        a.remove()
        window.URL.revokeObjectURL(url)
      }
      catch (e) {
        ElMessage({
          type: 'error',
          message: '下载失败，请稍后再试！',
        })
      }
    }

    const router = useRouter()
    const viewRecord = () => {
      router.push({
        name: 'record',
      })
    }

    onUnmounted(() => {
      cleanEventSource()
    })
    return () => (
      <div class="w-full h-full flex flex-wrap box-border bg-[#FFFFFF] content-start">
        {checkStatusMapBar[getSTATUS(activeStatus.value)]()}

        <div class="box-border p-[32px] w-full flex flex-nowrap">
          <div class="w-[142px] h-[166px] rounded-[8px] bg-[#f4f6f6] flex items-center justify-center">
            <FileType class="w-[82px] h-[100px]" type={frontTempFileInfo.value.fileType.ext} /> 
          </div>
         
          <div class="h-full grow pl-[48px] pb-[16px] flex flex-wrap content-between">
            {
              getSTATUS(activeStatus.value) !== getSTATUS(STATUS.CHECKING) && (
                <img src={statusIconMap[activeStatus.value]} class="absolute right-[30px] top-[30%]" />
              )
            }

            <div>
              <div class="text-[24px] text-[rgba(0,0,0,0.85)] mb-[8px]">{frontTempFileInfo.value.name}</div>
              <div class="text-[14px] text-[rgba(0,0,0,0.45)]">
                {getFileSize(frontTempFileInfo.value.size)}
                M
              </div>
            </div>
            <div class={`w-full ${progress}`}>
              <div class="mb-[8px] flex justify-between relative">
                {checkStatusMapTip[getSTATUS(activeStatus.value)]()}
                {getSTATUS(activeStatus.value) === getSTATUS(STATUS.CHECKING) && (
                  <div class="text-[36px] text-[#06b190] absolute right-0 bottom-[1px]">
                    82%
                  </div>
                )}
              </div>
              {
                getSTATUS(activeStatus.value) === getSTATUS(STATUS.CHECKING) && (
                  <el-progress
                    text-inside={true}
                    stroke-width={12}
                    percentage={50}
                  >
                    <div />
                  </el-progress>
                )
              }
            </div>
          </div>
        </div>

        <div class="box-border px-[32px] w-full">
          <el-divider style="margin:0" />
        </div>
        <div class="box-border px-[32px] w-full mt-[32px]">
          {
            getSTATUS(activeStatus.value) === getSTATUS(STATUS.SUCCESS) && paperUrl.value && (
              <el-button style="background: linear-gradient(90deg, #7ADC6A, #0FB48E);border: none; color:#fff;width: 100px;height: 40px;border-radius: 8px;font-size: 16px" onClick={downloadPaper}>
                下载报告
              </el-button>
            )
          }
          <el-button style="background: linear-gradient(90deg, #7ADC6A, #0FB48E);border: none; color:#fff;width: 100px;height: 40px;border-radius: 8px;font-size: 16px" onClick={reCheck}>
            再测一篇
          </el-button>
          <el-button style="width: 100px;height: 40px;border-radius: 8px;font-size: 16px;" onClick={viewRecord}>
            查看记录
          </el-button>
        </div>
      </div>
    )
  },
})
