const tableData = ref([])
const totalCount = ref(0)

const pageInfo = reactive({
  pageNum: 1,
  pageSize: 10,
})
const tableLoading = ref(false)
function startLoading() {
  tableLoading.value = true
}

function endLoading() {
  tableLoading.value = false
}
export function useTableList() {
  const getTableList = async (filterState?) => {
    startLoading()
    const params: any = {
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      taskName: filterState?.taskName,
      statusList: filterState?.statusList,
      paperName: filterState?.paperName,
      approvalStatusList: filterState?.approvalStatus,
      documentFormat: filterState?.documentFormat,
    }
    if (filterState && filterState.sortOrder)
      params.sortOrder = filterState.sortOrder
    const res = await paperList(params)

    // tableData.value = res.data.data.paperReviewRecordList
    tableData.value = res.data.data.records
    totalCount.value = res.data.data.totalCount
    endLoading()
    return res
  }

  return { tableData, totalCount, tableLoading, pageInfo, getTableList, startLoading, endLoading }
}