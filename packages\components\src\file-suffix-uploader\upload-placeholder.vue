<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  allowedTypes?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  allowedTypes: () => ['docx', 'pdf', 'zip'],
})

const allowedTypesText = computed(() => {
  return props.allowedTypes.map(type => type.toUpperCase()).join('、')
})
</script>

<template>
  <div class="flex flex-col items-center justify-center gap-2">
    <div class="text-4xl text-gray-400">
      📁
    </div>
    <div class="text-sm text-gray-500 text-center">
      <div>点击或拖拽文件到此处上传</div>
      <div class="text-xs text-gray-400 mt-1">
        支持的格式：{{ allowedTypesText }}
      </div>
    </div>
  </div>
</template>