import { resolve } from 'node:path'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [
    vue(),
    vueJsx({
      include: [/\.[jt]sx$/],
    }),
    AutoImport({
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
      ],
      imports: ['vue', 'vue-router', '@vueuse/core'],
      dts: 'src/typings/auto-imports.d.ts',
      dirs: [
        './packages/utils/src',
        './site/**/hooks/**',
        './site/**/service/**',
        // './src/**',
      ],
      vueTemplate: true,
    }),
  ],
  resolve: {
    alias: {
      '@': resolve('./src'),
      '@individual': resolve(__dirname, 'site/individual/'),
      '@comp': resolve('./packages/components/'),
    },
  },
  css: {
    preprocessorOptions: {
      scss: { api: 'modern-compiler' },
    },
  },
  server: {
    port: 8090,
    cors: true,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        // target: 'http://*************:5001',
        target: 'https://user-preview.papercheck.isrc.ac.cn:443/',
        changeOrigin: true,
        secure: false,
        // rewrite: path => path.replace(/^\/api/, ''),
      },
    },
  },
})
