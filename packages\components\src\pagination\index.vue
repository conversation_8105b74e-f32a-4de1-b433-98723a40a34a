<script setup lang="ts">
import type { ComponentSize } from 'element-plus'
import { CaretBottom } from '@element-plus/icons-vue'

const props = withDefaults(defineProps<{
  total: number 
  currentPage: number
  pageSize: number
  pageSizes?: number[]
  layout?: string
  size?: ComponentSize
}>(), {
  layout: 'slot, prev, pager, next',
  total: 0,
  pageSize: 10,
  pageSizes: () => [10, 20, 30, 50],
})
const emits = defineEmits(['handleSizeChange', 'handleCurrentChange'])

const pageSize = ref(props.pageSize)
const currentPage = ref(props.currentPage)

function handleSizeChange(val: number) {
  emits('handleSizeChange', val)
}
function handleCurrentChange(val: number) {
  emits('handleCurrentChange', val)
}
</script>

<template>
  <el-pagination
    v-model:current-page="currentPage"
    v-model:page-size="pageSize"
    :page-sizes="props.pageSizes"
    :size="size"
    :layout="layout"
    :total="props.total"
    v-bind="$attrs"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <template #default>
      <el-select 
        v-model="pageSize"
        :size="size" 
        style="width: 80px;" 
        :suffix-icon="CaretBottom" 
        @change="handleSizeChange"
      >
        <el-option 
          v-for="item in pageSizes" 
          :key="item" 
          :label="item" 
          :value="item"
        />
      </el-select>
      <div class="mx-[16px] mr-auto">
        总条数：{{ total }}
      </div>
    </template>
  </el-pagination>
</template>

<style scoped lang="scss">
:deep(.el-icon) {
  color: #1c1c1c;
}
:deep(.btn-prev .el-icon) {
  color: #1c1c1c;
  // transform: scale(1.5);
}
:deep(.btn-next .el-icon) {
  color: #1c1c1c;
  // transform: scale(1.5);
}
:deep(.el-pager li.is-active) {
  // padding: 16px 0;
  color: #ffffff;
  // width: 20px;
  // height: 24px;
  background-image: linear-gradient(90deg, #81df68 0%, #14b68c 100%);
}
:deep(.el-select__wrapper) {
  border-radius: 2px;
}
:deep(.el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 1px #14b68c inset;
}
</style>