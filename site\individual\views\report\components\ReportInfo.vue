<script setup lang="ts">
const reportDetail = inject('reportDetail') as any
// 默认数据
const defaultBasis = ref([``]) 
const defaultNotices = [
  // '请依据论文格式检测结果文档（docx 格式）中的批注信息进行相应修改。',
  '请注意，自动检测结果可能会有误判情况，建议您进行二次核实以确保结果的准确性。',
  '若您发现核查结果存在错误，或者软件运行出现异常情况，请联系 <EMAIL>，我们将尽快为您解决问题。',
]
watch(() => reportDetail.value, (val) => {
  defaultBasis.value[0] = `（1）学校模板${val && val.templateName}、（2）国家标准《GB7713学位论文编写格式》、《GB7714参考文献著录规则》、《GB15834标点符号用法》、《GB15835出版物上数字用法》、《GB3100国际单位制及其应用》、《GB3101有关量单位符号的一般原则》和《GB3102空间和时间的量和单位》。`
})
</script>

<template>
  <div class="w-full bg-[#F8F8F8] rounded-lg p-4 text-[14px] leading-[22px] opacity-85">
    <!-- 检测依据 -->
    <div class="flex flex-row">
      <!-- <div class="flex items-start">
        <span class="label whitespace-nowrap">1. 检测依据：</span>
      </div>
      <div class="content flex-1">
        1. 检测依据： {{ reportDetail && reportDetail.value?.checkBasis || defaultBasis[0] }}
      </div> -->
      <div class="content flex-1">
        1. 检测依据： {{ reportDetail && reportDetail.value?.checkBasis || defaultBasis[0] }}
      </div>  
    </div>

    <!-- 注意事项列表 -->
    <div v-if="reportDetail" class="mt-4">
      <div
        v-for="(notice, index) in (reportDetail.value?.notices || defaultNotices)" 
        :key="index" 
        class="flex flex-row gap-1 mt-4"
      >
        <div class="flex items-start">
          <span class="label whitespace-nowrap">{{ index + 2 }}. </span>
        </div>
        <div class="content flex-1">
          {{ notice }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
