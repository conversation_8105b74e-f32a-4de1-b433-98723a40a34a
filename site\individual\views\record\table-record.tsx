import type { TableInstance } from 'element-plus'
import { Delete, Download, View } from '@element-plus/icons-vue'
import { css } from '@emotion/css'
import { Modal, Pagination } from '@paper-review/components'
import day from 'dayjs'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import {
  deletePaperDetail,
  paperDownload,
  sendEmailReviewApi,
} from '../../service/api/sys'
import TeacherNameTable from './components/teacher-name-table/index.vue'

import { useTableList } from './index'

function renderHeader({ column }) {
  return (
    <div class="text-[rgba(0,0,0,0.85)] mb-[10px] font-[600]">
      {column.label}
    </div>
  )
}

const statusTag = {
  0: () => <el-tag type="info">排队中</el-tag>,
  1: () => <el-tag type="primary">检测中</el-tag>,
  2: () => <el-tag type="success">已完成</el-tag>,
  3: () => <el-tag type="danger">检测失败</el-tag>,
}

const approvalStatusTag = {
  0: () => <el-tag type="info">未提交</el-tag>,
  1: () => <el-tag type="primary">审核中</el-tag>,
  2: () => <el-tag type="danger">不通过</el-tag>,
  3: () => <el-tag type="success">审核通过</el-tag>,
}

function useGenerateStyle() {
  const generateCellStyle = ({ row, column, rowIndex, columnIndex }) => {
    // 处理全选按钮
    if (columnIndex === 0 && rowIndex === 0) {
      return {
        paddingBottom: '15px',
      }
    }
  }

  const selectionAll = ref(css`
    & .cell {
      opacity: 0;
    }
  `)
  const generateHeaderClassName = ({ row, column, rowIndex, columnIndex }) => {
    // 处理全选按钮
    if (columnIndex === 0 && rowIndex === 0) {
      return selectionAll.value
    }
  }

  const checkCellClassName = ref(css`
    & .check-selection .cell {
      opacity: 0;
    }
    &:hover .check-selection .cell {
      opacity: 1;
    }
  `)

  const generateCellClassName = () => {
    return checkCellClassName.value
  }
  return {
    checkCellClassName,
    selectionAll,
    generateHeaderClassName,
    generateCellStyle,
    generateCellClassName,
  }
}

export default defineComponent({
  name: 'TableRecord',
  props: {
    filterState: Object,
    tableSelection: Array,
    viewType: String,
  },
  emits: ['select', 'delete', 'preview'],
  setup(props, { emit }) {
    const router = useRouter()
    const {
      checkCellClassName,
      selectionAll,
      generateHeaderClassName,
      generateCellStyle,
      generateCellClassName,
    } = useGenerateStyle()
    const tableRef = useTemplateRef<TableInstance>('table')
    const selectedRowNums = ref(0)

    const {
      tableLoading,
      startLoading,
      endLoading,
      tableData,
      totalCount,
      getTableList,
      pageInfo,
    } = useTableList()

    const preview = (row) => {
      if (row.taskStatus !== '2') {
        ElMessage({
          message: '当前状态不可查看',
          type: 'warning',
        })
        return
      }
      emit('preview', row)
    }

    const deleteTableItems = async (scope, e) => {
      e.stopPropagation()
      startLoading()
      await deletePaperDetail([scope.row.id])
      getTableList()
      endLoading()
    }

    const download = async (scope, e) => {
      if (scope.row.taskStatus !== '2' || !scope.row.zipUrl) {
        ElMessage({
          message: '当前状态不可下载',
          type: 'warning',
        })
        return
      }
      e.stopPropagation()
      startLoading()
      try {
        const res = await paperDownload({
          fileUrls: [scope.row.zipUrl].toString(),
        })
        if (res.response.data.status !== 200) {
          ElMessage({
            message: '文件下载失败，请稍后重试',
            type: 'error',
          })
          return endLoading()
        }
        const date = day().format('YYYYMMDD')
        const blobObj = new Blob([res.data], { type: 'application/zip' })
        const url = window.URL.createObjectURL(blobObj)
        const a = document.createElement('a')
        a.href = url
        a.download = `论文检测系统_${date}.zip`
        document.body.appendChild(a)
        a.click()
        a.remove()
        window.URL.revokeObjectURL(url)
      }
      catch (e) {
        ElMessage({
          type: 'error',
          message: '下载失败，请稍后再试！',
        })
      }
      finally {
        endLoading()
      }
    }

    const tableSelect = (selection) => {
      selectedRowNums.value = selection.length
      emit('select', selection)
    }

    const selectAll = (selection) => {
      selectedRowNums.value = selection.length
      emit('select', selection)
    }

    const rowClick = (row) => {
      tableRef.value?.toggleRowSelection(row)
      tableSelect(tableRef.value?.getSelectionRows())
    }
    const handleSizeChange = (val) => {
      pageInfo.pageSize = val
    }

    const handleCurrenhantChange = (val) => {
      pageInfo.pageNum = val
    }

    watch(
      () => props.tableSelection,
      (val) => {
        if (!val?.length) {
          tableRef.value?.clearSelection()
          selectedRowNums.value = 0
        }
      },
    )

    watch(
      () => pageInfo,
      () => {
        getTableList()
      },
      { deep: true },
    )

    watch(
      () => props.viewType,
      () => {
        const selectIds = props.tableSelection?.map(item => (item as any).id)
        tableData.value.forEach((item: any) => {
          item.checked = selectIds?.includes(item.id)
          tableRef.value?.toggleRowSelection(item, item.checked)
        })
        tableSelect(props.tableSelection)
      },
      {
        immediate: true,
      },
    )

    watch(
      () => selectedRowNums.value,
      (val) => {
        if (val) {
          selectionAll.value = css`
            & .cell {
              opacity: 1;
            }
          `
          checkCellClassName.value = css`
            & .check-selection .cell {
              opacity: 1;
            }
          `
        }
        else {
          selectionAll.value = css`
            & .cell {
              opacity: 0;
            }
          `
          checkCellClassName.value = css`
            & .check-selection .cell {
              opacity: 0;
            }
            &:hover .check-selection .cell {
              opacity: 1;
            }
          `
        }
      },
    )

    /** =============教师审核============== */
    const teacherModalVisible = ref(false)

    const currentTaskRow = ref()
    const handleSubmitDocumentToCheck = (scope, e) => {
      /**
       * 检测状态 0.排队中 1.检测中 2.已完成 3.检测失败
       * 审核状态 0.未提交 1.审核中 2.不通过 3.初步审核通过 4.最终审核通过
       * 1. 排队中/检测中/检测失败
       *  或者 审核状态通过/不通过
       *  - 提交按钮变灰色，点击提示“当前状态不可提交审核”
       * 2. 已完成 审核中 - 提示 “该检测报告正在审核中，请问是否重复提交？【确认】【取消】”
       * 3. 已完成 未提交 - 调接口检测是否有相同论文
       *   3.1 有相同论文 - 提示“同篇论文有正在审核的检测报告，请问是否重复提交？【确认】【取消】”
       *   3.2 无相同论文 - 继续提交审核
       */

      // 1. 排队中/检测中/检测失败 或者 审核状态通过/不通过 不可提交
      if (
        scope.row.taskStatus === '0'
        || scope.row.taskStatus === '1'
        || scope.row.taskStatus === '3'
        || scope.row.approvalStatus === '2'
        || scope.row.approvalStatus === '3'
        || scope.row.approvalStatus === '4'
      ) {
        ElMessage({
          message: '当前状态不可提交审核',
          type: 'warning',
        })
      }

      // 2. 已完成 审核中 - 提示 “该检测报告正在审核中，请问是否重复提交？【确认】【取消】”
      else if (
        scope.row.taskStatus === '2'
        && scope.row.approvalStatus === '1'
      ) {
        ElMessageBox.confirm('该检测报告正在审核中，请问是否重复提交？', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            setTimeout(() => {
              currentTaskRow.value = scope.row
              teacherModalVisible.value = true
            }, 200)

            return 0
          })
          .catch(() => {
            // eslint-disable-next-line no-console
            console.log('取消提交')
            return 0
          })
      }

      // 3. 已完成 未提交 - 调接口检测是否有相同论文
      // else if (
      //   scope.row.taskStatus === '2'
      //   && scope.row.approvalStatus === '0'
      // ) {
      // 暂时不做 TODO
      // const res = await checkSamePaper({
      //   taskId: scope.row.id,
      // })
      // if (res.response.data.status === 200) {

      //   ElMessageBox.confirm(
      //     '同篇论文有正在审核的检测报告，请问是否重复提交？',
      //     {
      //       confirmButtonText: '确认',
      //       cancelButtonText: '取消',
      //       type: 'warning',
      //     },
      //   )
      //     .then(() => {
      //       // eslint-disable-next-line no-console
      //       console.log('继续提交')
      //     })
      //     .catch(() => {
      //       // eslint-disable-next-line no-console
      //       console.log('取消提交')
      //       return 0
      //     })
      // }
      // }

      // 4. 提交审核
      else {
        currentTaskRow.value = scope.row
        teacherModalVisible.value = true
      }
    }

    /**
     * 提交给教师审核
     */
    const teacherNameTableRef = ref()

    async function handleSubmitToTeacherCheck() {
      const teacherName = teacherNameTableRef.value?.currentRow?.teacherName
      const teacherEmail = teacherNameTableRef.value?.currentRow?.teacherEmail

      if (!teacherName || !teacherEmail) {
        ElMessage({
          message: '请先选择教师',
          type: 'warning',
        })
        return
      }

      // ======= 提交审核时，显示 loading 效果 =======
      const teacherModalLoadingService = ElLoading.service({
        target: '.teacher-check-custom-modal',
        fullscreen: false,
      })

      try {
        const res = await sendEmailReviewApi({
          ...currentTaskRow.value,
          teacherEmail,
          teacherName,
        })

        if (res.response.data.status === 200) {
          ElMessage({
            message: res.response.data.data.msg,
            type: 'success',
          })
          getTableList()
          teacherModalVisible.value = false
        }
        else {
          ElMessage({
            message: '提交审核失败',
            type: 'error',
          })
        }
      }
      catch (error) {
        ElMessage({
          message: '提交审核失败',
          type: 'error',
        })
      }
      finally {
        // ======= 提交审核时，关闭 loading 效果 =======
        teacherModalLoadingService.close()
      }
    }
    /** =============教师审核============== */

    return () => {
      return (
        <div
          v-loading={tableLoading.value}
          style="width: 100%;height:600px;margin-top:10px;padding-bottom:100px;"
          class="relative"
        >
          <el-table
            ref="table"
            height="550"
            stripe
            onRowClick={rowClick}
            onSelect={tableSelect}
            row-class-name={generateCellClassName}
            header-cell-style={generateCellStyle}
            header-cell-class-name={generateHeaderClassName}
            data={tableData.value}
            style={{ color: 'rgba(0,0,0,0.85)' }}
            row-style={{ cursor: 'pointer', height: '48px' }}
            onSelectAll={selectAll}
          >
            <el-table-column
              width="30"
              type="selection"
              class-name="check-selection"
            />
            <el-table-column
              width="350px"
              show-overflow-tooltip
              prop="taskName"
              label="任务名称"
              render-header={renderHeader}
            />
            <el-table-column
              show-overflow-tooltip
              prop="author"
              label="作者"
              render-header={renderHeader}
            />
            <el-table-column
              show-overflow-tooltip
              prop="taskDocumentFormat"
              label="论文类型"
              render-header={renderHeader}
            />
            <el-table-column
              width="200px"
              show-overflow-tooltip
              prop="createTime"
              label="提交时间"
              render-header={renderHeader}
            />
            <el-table-column
              show-overflow-tooltip
              prop="taskStatus"
              label="检测状态"
              render-header={renderHeader}
            >
              {{
                default({ row }) {
                  return statusTag[row.taskStatus || 0]()
                },
              }}
            </el-table-column>

            {/* <el-table-column 
              show-overflow-tooltip 
              prop="approvalStatus" 
              label="审核状态" 
              render-header={renderHeader}
            >
              {{
                default({ row }) {
                  if (row.taskStatus !== '2') {
                    return '-'
                  }
                  return approvalStatusTag[row.approvalStatus || '0']()
                },
              }}
            </el-table-column> */}

            <el-table-column label="操作" render-header={renderHeader}>
              {{
                default(scope) {
                  return (
                    <div
                      class="flex flex-nowrap justify-start"
                      onClick={e => e.stopPropagation()}
                    >
                      {/* <el-tooltip
                        content="提交审核"
                        placement="top"
                        show-after={1000}
                        class="pointer-events-none"
                      >
                        <div
                          aria-label="提交给教师审核按钮"
                          onClick={e => handleSubmitDocumentToCheck(scope, e)}
                          class={
                            cn(
                              'w-[24px] h-[24px]',
                              'flex-shrink-0 flex justify-center items-center ',
                              'rounded-[8px]',
                              // 提交状态
                              scope.row.taskStatus === '0' && 'text-[#e9e9e9]', // 未提交
                              scope.row.taskStatus === '1' && 'text-[#e9e9e9] ', // 审核中
                              scope.row.taskStatus === '2' && 'text-currentColor ', // 检测完成
                              scope.row.taskStatus === '3' && 'text-[#e9e9e9] ', // 检测失败

                              // 审核状态 0.未提交 1.审核中 2.不通过 3.初步审核通过 4.最终审核通过
                              scope.row.approvalStatus === '0' && 'text-currentColor ', // 未提交审核
                              scope.row.approvalStatus === '1' && 'text-currentColor ', // 审核中
                              scope.row.approvalStatus === '2' && 'text-[#e9e9e9] ', // 不通过
                              scope.row.approvalStatus === '3' && 'text-[#e9e9e9] ', // 审核通过
                              scope.row.approvalStatus === '4' && 'text-[#e9e9e9] ', // 审核通过

                              // hover 状态
                              scope.row.taskStatus === '2' 
                              && (scope.row.approvalStatus === '0' || scope.row.approvalStatus === '1') 
                              && 'hover:bg-bg-active hover:text-active', // 检测完成 未提交审核 审核中 的时候， hover 有状态
                            )
                          }
                        >
                          <svg class="w-4 h-4 cursor-pointer" viewBox="0 0 16 16">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                              <g transform="translate(-1198.000000, -236.000000)">
                                <g transform="translate(156.000000, 168.000000)">
                                  <g transform="translate(16.000000, 62.000000)">
                                    <g transform="translate(1026.000000, 6.000000)">
                                      <rect x="0" y="0" width="16" height="16"></rect>
                                      <rect x="0" y="0" width="16" height="16"></rect>
                                      <path fill="currentColor" d="M10.5,7.5 C12.4329966,7.5 14,9.06700338 14,11 C14,11.7653622 13.7543359,12.4733464 13.3375588,13.0494016 L14.8774867,14.6537907 C15.0686928,14.8530264 15.0621836,15.1695419 14.862948,15.360748 C14.6858496,15.530709 14.4160856,15.5444505 14.2240343,15.4054763 L14.1559907,15.3462093 L12.6412616,13.7687843 C12.0493945,14.2271785 11.3065577,14.5 10.5,14.5 C8.56700338,14.5 7,12.9329966 7,11 C7,9.06700338 8.56700338,7.5 10.5,7.5 Z M10.5,8.5 C9.11928813,8.5 8,9.61928813 8,11 C8,12.3807119 9.11928813,13.5 10.5,13.5 C11.8807119,13.5 13,12.3807119 13,11 C13,9.61928813 11.8807119,8.5 10.5,8.5 Z M10.5,0.5 C11.8254834,0.5 12.9100387,1.53153594 12.9946823,2.83562431 L13,3 L13,6.74489842 C13,7.02104079 12.7761424,7.24489842 12.5,7.24489842 C12.2545401,7.24489842 12.0503916,7.06802326 12.0080557,6.83477405 L12,6.74489842 L12,3 C12,2.22030388 11.4051119,1.57955132 10.64446,1.50686658 L10.5,1.5 L3.5,1.5 C2.72030388,1.5 2.07955132,2.09488808 2.00686658,2.85553999 L2,3 L2,10 C2,10.7796961 2.59488808,11.4204487 3.35553999,11.4931334 L3.5,11.5 L5.70940931,11.5 C5.98555168,11.5 6.20940931,11.7238576 6.20940931,12 C6.20940931,12.2454599 6.03253415,12.4496084 5.79928494,12.4919443 L5.70940931,12.5 L3.5,12.5 C2.1745166,12.5 1.08996133,11.4684641 1.00531768,10.1643757 L1,10 L1,3 C1,1.6745166 2.03153594,0.589961328 3.33562431,0.50531768 L3.5,0.5 L10.5,0.5 Z M4.78137526,8 C5.05751763,8 5.28137526,8.22385763 5.28137526,8.5 C5.28137526,8.74545989 5.1045001,8.94960837 4.87125089,8.99194433 L4.78137526,9 L3.78137526,9 C3.50523289,9 3.28137526,8.77614237 3.28137526,8.5 C3.28137526,8.25454011 3.45825042,8.05039163 3.69149963,8.00805567 L3.78137526,8 L4.78137526,8 Z M5.78137526,6 C6.05751763,6 6.28137526,6.22385763 6.28137526,6.5 C6.28137526,6.74545989 6.1045001,6.94960837 5.87125089,6.99194433 L5.78137526,7 L3.78137526,7 C3.50523289,7 3.28137526,6.77614237 3.28137526,6.5 C3.28137526,6.25454011 3.45825042,6.05039163 3.69149963,6.00805567 L3.78137526,6 L5.78137526,6 Z M9.78137526,4.0376182 C10.0575176,4.0376182 10.2813753,4.26147583 10.2813753,4.5376182 C10.2813753,4.78307809 10.1045001,4.98722657 9.87125089,5.02956253 L9.78137526,5.0376182 L3.78137526,5.0376182 C3.50523289,5.0376182 3.28137526,4.81376057 3.28137526,4.5376182 C3.28137526,4.29215831 3.45825042,4.08800983 3.69149963,4.04567387 L3.78137526,4.0376182 L9.78137526,4.0376182 Z"></path>
                                    </g>
                                  </g>
                                </g>
                              </g>
                            </g>
                          </svg>
                        </div>
                      </el-tooltip> */}

                      <el-tooltip
                        content="查看报告"
                        placement="top"
                        show-after={1000}
                        class="pointer-events-none"
                      >
                        <div
                          class="p-[5px] flex justify-center items-center rounded-[8px]"
                          onClick={() => preview(scope.row)}
                        >
                          <View
                            class="w-[14px] h-[14px] cursor-pointer"
                            style={{
                              color:
                                scope.row.taskStatus !== '2' ? '#e9e9e9' : '',
                            }}
                          />
                        </div>
                      </el-tooltip>

                      <div
                        class="p-[5px] flex justify-center items-center rounded-[8px] hover:bg-bg-active"
                        onClick={e => download(scope, e)}
                      >
                        <el-tooltip
                          content="下载报告"
                          placement="top"
                          show-after={1000}
                          class="pointer-events-none"
                        >
                          <Download
                            class="w-[14px] h-[14px] cursor-pointer hover:text-active"
                            style={{
                              color:
                                scope.row.taskStatus !== '2'
                                || !scope.row.zipUrl
                                  ? '#e9e9e9'
                                  : '',
                            }}
                          />
                        </el-tooltip>
                      </div>
                      <el-tooltip
                        content="删除"
                        placement="top"
                        show-after={1000}
                        class="pointer-events-none"
                      >
                        <el-popconfirm
                          title="确认删除?"
                          onConfirm={e => deleteTableItems(scope, e)}
                          v-slots={{
                            reference: () => (
                              <div class="p-[5px] flex justify-center items-center rounded-[8px] hover:bg-bg-active">
                                <Delete class="w-[14px] h-[14px] cursor-pointer hover:text-active" />
                              </div>
                            ),
                          }}
                        >
                        </el-popconfirm>
                      </el-tooltip>
                    </div>
                  )
                },
              }}
            </el-table-column>
          </el-table>
          <Pagination
            class="absolute bottom-0 right-0"
            total={totalCount.value}
            pageSize={pageInfo.pageSize}
            currentPage={pageInfo.pageNum}
            onHandleSizeChange={handleSizeChange}
            onHandleCurrentChange={handleCurrenhantChange}
          />

          {/* 提交给教师审核的 Modal */}
          <Modal
            v-model:dialog-visible={teacherModalVisible.value}
            width={400}
            class="teacher-check-custom-modal"
            title="提交审核"
            v-slots={{
              footer: () => (
                <>
                  <el-button
                    type="primary"
                    class={cn(
                      'hover:opacity-80 !border-0 bg-gradient-to-r from-[#6ED86E] to-[#1DB989]',
                      'w-[60px] h-[32px]',
                    )}
                    onClick={() => handleSubmitToTeacherCheck()}
                  >
                    确认
                  </el-button>

                  <el-button
                    class=" w-[60px] h-[32px] "
                    onClick={() => (teacherModalVisible.value = false)}
                  >
                    取消
                  </el-button>
                </>
              ),
            }}
          >
            <TeacherNameTable ref={teacherNameTableRef} />
          </Modal>
        </div>
      )
    }
  },
})
