<script setup lang="ts">
import { FileSuffixUploader } from "@paper-review/components";
import { ref } from "vue";

import PaperSuffixUploader from "./sample/PaperSuffixUploader.vue";

const message = ref("");
const messageType = ref<"success" | "error">("success");

function handleFileSelected(file: File) {
  message.value = `文件选择成功: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
  messageType.value = "success";

  setTimeout(() => {
    message.value = "";
  }, 3000);
}

function handleFileRejected(file: File, reason: string) {
  message.value = `文件被拒绝: ${file.name} - ${reason}`;
  messageType.value = "error";

  setTimeout(() => {
    message.value = "";
  }, 3000);
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">
        FileSuffixUploader 重构演示
      </h1>

      <!-- 业务组件样式 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">业务组件样式</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">论文检测上传</h3>
            <PaperSuffixUploader class="w-[636px] h-[320px]" />
          </div>
        </div>
      </section>

      <!-- 基础用法 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">基础用法</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">默认样式</h3>
            <FileSuffixUploader
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">自定义默认内容</h3>
            <FileSuffixUploader
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            >
              <template #default>
                <div class="text-center">
                  <div class="text-3xl mb-2">🎯</div>
                  <div class="text-sm text-gray-600">自定义上传区域</div>
                  <div class="text-xs text-gray-400 mt-1">
                    支持 DOCX、PDF、ZIP
                  </div>
                </div>
              </template>
            </FileSuffixUploader>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">禁用状态</h3>
            <FileSuffixUploader
              disabled
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            />
          </div>
        </div>
      </section>

      <!-- 动态文件类型配置 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          动态文件类型配置
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">只允许 DOCX</h3>
            <FileSuffixUploader
              :allowed-types="['docx']"
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">只允许 PDF</h3>
            <FileSuffixUploader
              :allowed-types="['pdf']"
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">DOCX + PDF</h3>
            <FileSuffixUploader
              :allowed-types="['docx', 'pdf']"
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            />
          </div>
        </div>
      </section>

      <!-- 自定义文件类型 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          自定义文件类型
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">添加 PPT 支持</h3>
            <FileSuffixUploader
              :allowed-types="['docx', 'pdf', 'ppt']"
              :custom-file-types="{
                ppt: {
                  mimeTypes: [
                    'application/vnd.ms-powerpoint',
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                  ],
                  extensions: ['.ppt', '.pptx'],
                  icon: '📊',
                  label: 'PPT 演示文稿',
                },
              }"
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            >
              <template #ppt-content="{ config }">
                <div class="text-center">
                  <div class="text-2xl text-purple-600">
                    {{ config.icon }}
                  </div>
                  <div class="text-xs text-purple-700 mt-1">
                    {{ config.label }}
                  </div>
                </div>
              </template>
            </FileSuffixUploader>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">添加 Excel 支持</h3>
            <FileSuffixUploader
              :allowed-types="['xlsx', 'csv']"
              :custom-file-types="{
                xlsx: {
                  mimeTypes: [
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                  ],
                  extensions: ['.xlsx'],
                  icon: '📈',
                  label: 'Excel 表格',
                },
                csv: {
                  mimeTypes: ['text/csv'],
                  extensions: ['.csv'],
                  icon: '📋',
                  label: 'CSV 文件',
                },
              }"
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            />
          </div>
        </div>
      </section>

      <!-- 文件类型 Slot 定制 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          文件类型 Slot 定制
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">自定义 DOCX 样式</h3>
            <FileSuffixUploader
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            >
              <template #docx-content>
                <div class="text-center">
                  <div
                    class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2"
                  >
                    <span class="text-white text-xl">📝</span>
                  </div>
                  <div class="text-sm font-medium text-blue-700">Word 文档</div>
                  <div class="text-xs text-blue-600">准备添加</div>
                </div>
              </template>
            </FileSuffixUploader>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">自定义 PDF 样式</h3>
            <FileSuffixUploader
              @file-selected="handleFileSelected"
              @file-rejected="handleFileRejected"
            >
              <template #pdf-content>
                <div class="text-center">
                  <div
                    class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mx-auto mb-2"
                  >
                    <span class="text-white text-xl">📄</span>
                  </div>
                  <div class="text-sm font-medium text-red-700">PDF 文档</div>
                  <div class="text-xs text-red-600">准备添加</div>
                </div>
              </template>
            </FileSuffixUploader>
          </div>
        </div>
      </section>

      <!-- 完整定制示例 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">完整定制示例</h2>
        <div class="max-w-md">
          <FileSuffixUploader
            :allowed-types="['docx', 'pdf']"
            @file-selected="handleFileSelected"
            @file-rejected="handleFileRejected"
          >
            <template #default>
              <div class="text-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-3"
                >
                  <span class="text-white text-2xl">📁</span>
                </div>
                <div class="text-lg font-medium text-gray-700 mb-1">
                  拖拽文件到这里
                </div>
                <div class="text-sm text-gray-500">或点击选择文件</div>
                <div class="text-xs text-gray-400 mt-2">
                  仅支持 Word 和 PDF 文件
                </div>
              </div>
            </template>

            <template #docx-content>
              <div class="text-center">
                <div
                  class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3 animate-pulse"
                >
                  <span class="text-white text-2xl">📝</span>
                </div>
                <div class="text-lg font-medium text-blue-700">Word 文档</div>
                <div class="text-sm text-blue-600">松手即可上传</div>
              </div>
            </template>

            <template #pdf-content>
              <div class="text-center">
                <div
                  class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-3 animate-pulse"
                >
                  <span class="text-white text-2xl">📄</span>
                </div>
                <div class="text-lg font-medium text-red-700">PDF 文档</div>
                <div class="text-sm text-red-600">松手即可上传</div>
              </div>
            </template>

            <template #forbidden-content>
              <div class="text-center">
                <div
                  class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3"
                >
                  <span class="text-white text-2xl">🚫</span>
                </div>
                <div class="text-lg font-medium text-red-700">不支持的格式</div>
                <div class="text-sm text-red-600">请选择 Word 或 PDF 文件</div>
              </div>
            </template>
          </FileSuffixUploader>
        </div>
      </section>

      <!-- API 文档 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">API 文档</h2>
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4">Props</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    属性
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    类型
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    默认值
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    说明
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    allowedTypes
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    string[]
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ['docx', 'pdf', 'zip']
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    允许的文件类型
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    customFileTypes
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    Record&lt;string, FileTypeConfig&gt;
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {}
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    自定义文件类型配置，可扩展内置类型
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    disabled
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    boolean
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    false
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">是否禁用</td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    class
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    string
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    自定义 CSS 类名
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <h3 class="text-lg font-medium text-gray-700 mb-4 mt-8">Events</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    事件名
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    参数
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    说明
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    fileSelected
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    file: File
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    文件选择成功时触发
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    fileRejected
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    file: File, reason: string
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    文件被拒绝时触发
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    dragEnter
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    拖拽进入时触发
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    dragLeave
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    拖拽离开时触发
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <h3 class="text-lg font-medium text-gray-700 mb-4 mt-8">Slots</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    插槽名
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    参数
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    说明
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    default
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    默认状态下的内容
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    {fileType}-content
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    variant, fileType, config
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    动态文件类型 slot，如 docx-content、pdf-content 等
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    forbidden-content
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    variant, fileType, config
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    不支持的文件类型时的内容
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- 消息显示 -->
      <div v-if="message" class="fixed bottom-4 right-4 max-w-sm">
        <div
          class="p-4 rounded-lg shadow-lg"
          :class="
            messageType === 'success'
              ? 'bg-green-500 text-white'
              : 'bg-red-500 text-white'
          "
        >
          {{ message }}
        </div>
      </div>
    </div>
  </div>
</template>
