export const whiteList = ['login', 'review']

export const basicRoutes = [
  {
    path: '/',
    redirect: '/review', 
  },
  {
    path: '/404',
    name: '404',
    component: () => import('../../views/404/index.vue'),
  },

  {
    path: '/teacher-check-operate',
    name: 'teacher-check-operate',
    component: () => import('../../views/teacher-check/operate/index.vue'),
  },

  {
    path: '/report',
    name: 'report',
    component: () => import('../../views/report/index.vue'),
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../../views/login/index.vue'),
  },
  {
    path: '/policy',
    name: 'policy',
    component: () => import('../../views/policy/index.vue'),
  },
  {
    path: '/privacy',
    name: 'privacy',
    component: () => import('../../views/policy/privacy.vue'),
    
  },
  {
    path: '/manual-check',
    name: 'manualCheck',
    component: () => import('../../views/manualCheck/index.vue'),
    
  },
  {
    path: '/content',
    name: 'content',
    component: () => import('../../layout/index.vue'),
    children: [
      {
        path: '/review',
        name: 'review',
        component: () => import('../../views/review/index.vue'),
      },
      {
        path: '/record',
        name: 'record',
        component: () => import('../../views/record/index.vue'),
      },
    ],
  },
]