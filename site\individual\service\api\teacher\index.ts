/**
 * 教师端 API 接口
 * 所有的教师端的 API 接口，都写在这里
 * TODO: 后续拆分项目的时候，直接挪走就好了
 */

import { request } from '../../request'

/**
 * 教师端验证教师 token
 * @param data 
 * @returns 
 */
export function verifyTeacherTokenApi(data?: any) {
  return request({
    url: '/review/verifyTeacherToken',
    method: 'post',
    data,
  })
}

/**
 * 教师端获取论文审核记录
 * @param data 
 * @returns 
 */
export function getPaperReviewRecordsApi(data?: any) {
  return request({
    url: '/review/getPaperReviewRecords',
    method: 'post',
    data,
  })
}

/**
 * 教师端更新论文审核记录
 * @param data 
 * @returns 
 */
export function updatePaperReviewApi(data?: any) {
  return request({
    url: '/review/updatePaperReview',
    method: 'post',
    data,
  })
}

/**
 * 教师端下载论文
 * @param data 
 * @returns 
 */
export function paperDownloadApi(data: any) {
  return request({
    responseType: 'blob',
    url: '/review/download',
    method: 'post',
    data,
  })
}