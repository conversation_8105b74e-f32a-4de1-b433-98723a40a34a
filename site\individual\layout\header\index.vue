<script setup lang="ts">
import { getToken } from '../../store/modules/user.ts'
import AppLogo from './components/app-logo.tsx'
import UserKeyInfo from './components/user-key-info.vue'

const router = useRouter()
const route = useRoute()

const pageActive = ref('review')
function switchPage(page: string) {
  if (page === 'record' && !getToken()) {
    return router.push({ name: 'login', query: { type: 'login' } })
  }
  pageActive.value = page
  router.push({ name: page })
}

const isToken = ref(getToken() || '') 
const isUserInfo = localStg.get('userInfo') || ''

function registerPage() {
  router.push({ name: 'login', query: { type: 'register' } })
}
function loginPage() {
  router.push({ name: 'login', query: { type: 'login' } })
}
</script>

<template>
  <div class="layout-header w-[100%] h-[65px] bg-[#fff] flex flex-nowrap justify-between">
    <div class="w-fit h-full items-center flex flex-nowrap cursor-pointer">
      <AppLogo />
      <span class="text-[20px] font-bold ml-[12px]">论文格式检测系统</span>   
      <div class="ml-[12rem] content-center h-full flex flex-nowrap">
        <span class="text-[16px] flex items-center cursor-pointer mr-[4.8rem] " :class="route.name === 'review' ? ' tab-active' : ''" @click="switchPage('review')">论文检测</span>
        <span class="text-[16px] flex items-center cursor-pointer" :class="route.name === 'record' ? ' tab-active' : ''" @click="switchPage('record')">检测记录</span>
      </div>
    </div>
    <div class="flex flex-nowrap items-center">
      <el-button v-show="!isToken" class="mr-[12px]" @click="registerPage">
        免费使用
      </el-button>
      <el-button v-show="!isToken" class="mr-[20px]" style="background: linear-gradient(90deg, #6ED86E, #1DB989);color: #fff;border: none;" @click="loginPage">
        登录
      </el-button>
      <UserKeyInfo v-show="isToken" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.layout-header {
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
}
.tab-active {
  display: flex;
  align-items: center;
  position: relative;
  color: #06b190;
  height: 100%;
  &::after {
    position: absolute;
    height: 2px;
    box-sizing: border-box;
    width: 100%;
    content: '';
    display: inline-block;
    background-color: #06b190;
    bottom: -1px;
    left: 0;
  }
}
</style>