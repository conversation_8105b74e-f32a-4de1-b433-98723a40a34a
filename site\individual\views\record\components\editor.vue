<script setup lang="ts">
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import WangEditor from 'wangeditor'

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['getHtml'])
// DOM
const editor = ref()
// 实例
let instance = reactive<any>({})
watch(() => props.content, (val) => {
  // 设置内容
  if (val !== instance.txt.html()) {
    instance.txt.html(val) // 根据父组件传来的值设置html值
  }
})
onMounted(() => {
  instance = new WangEditor(editor.value)
  Object.assign(instance.config, {
    onchange() {
      emit('getHtml', instance.txt.html())
    },
  })
  instance.config.pasteFilterStyle = true
  instance.config.pasteIgnoreImg = false
  instance.config.uploadImgShowBase64 = true
  instance.config.showLinkImg = false
  instance.config.showFullScreen = false
  // 配置菜单栏，设置不需要的菜单
  //   console.log(instance.config)
  instance.config.menus = [
    'bold', // 加粗
    'lineHeight', // 调整行间距
    'italic', // 斜体
    'underline', // 下划线
    'color', // 改变文字颜色
    'image', // 插入图片
    'splitLine',
  ] // 加分割线]
  instance.create()
})

onBeforeUnmount(() => {
  instance.destroy()
  instance = null
})
    
defineExpose({
  isValid() {
    const html = instance.txt.html()
    if (html.includes('<img')) 
      return true
    const text = instance.txt.text()
    const ans = text.replace(/&nbsp;/g, '').trim()
    if (ans === '') {
      return false
    }
    else {
      return true
    }
  },
  clear() {
    instance.txt.clear()
  },
})
</script>
    
<template>
  <div ref="editor" />
</template>
    
<style lang="scss">
.w-e-text {
  ul li {
    list-style: disc;
  }
  ol li {
    list-style: decimal;
  }
}
.w-e-toolbar {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-color: rgba(0, 0, 0, 0.15) !important;
}
.w-e-text-container {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-color: rgba(0, 0, 0, 0.15) !important;
}
</style>
