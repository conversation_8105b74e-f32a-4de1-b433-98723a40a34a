<script setup>
import ListHeader from '../components/ListHeader.vue'
import Table from '../components/Table.vue'
import Title from '../components/Title.vue'
import CheckResultInfo from './components/CheckResultInfo.vue'
import PaperBaseInfo from './components/PaperBaseInfo.vue'
import ReportInfo from './components/ReportInfo.vue'

const historyData = ref([
  {
    name: '王新元_面向容器编排平台的自动化调优系统研究V4',
    formatErrors: 356,
    semanticErrors: 51,
    suggestions: 17,
    time: '2025-01-13 12:57:23',
  },
  {
    name: '王新元_面向容器编排平台的自动化调优系统研究V3',
    formatErrors: 408,
    semanticErrors: 67,
    suggestions: 23,
    time: '2025-01-12 15:27:56',
  },
  {
    name: '王新元_面向容器编排平台的自动化调优系统研究V2',
    formatErrors: 513,
    semanticErrors: 89,
    suggestions: 36,
    time: '2025-01-10 08:35:09',
  },
  {
    name: '王新元_面向容器编排平台的自动化调优系统研究V1',
    formatErrors: 643,
    semanticErrors: 113,
    suggestions: 57,
    time: '2025-01-04 21:09:45',
  },
])

const reportInfo = ref({
  checkBasis: ['学校模板《清华大学硕士毕业论文撰写规范》...'],
  notices: [
    // '请依据论文格式检测结果文档（docx 格式）中的批注信息进行相应修改。',
    '请注意，自动检测结果可能会有误判情况，建议您进行二次核实以确保结果的准确性。',
    '若您发现核查结果存在错误，或者软件运行出现异常情况，请联系 <EMAIL>，我们将尽快为您解决问题。',
  ],
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm p-8">
      <!-- Title Section -->
      <Title 
        title="王新元《面向容器编排平台的自动化调优系统研究》" 
        time="2025-01-13 12:57:23"
      />

      <!-- 审核结果 -->
      <div class="mb-8">
        <ListHeader
          chinese="审核结果"
          english="AUDIT RESULT"
        />
        <CheckResultInfo
          :check-result="{
            status: '通过',
            checker: '余华',
            checkTime: '2025-01-011 09:00:45',
            comments: '标题、章节层级不规范，如第二章节第2句话不正确。请仔细检查！...',
          }"
        />
      </div>

      <!-- 论文基本信息 -->
      <div class="mb-8">
        <ListHeader
          chinese="论文信息"
          english="Paper Information"
        />
        <PaperBaseInfo
          :paper-info="{
            title: '张三三的毕业论文',
            author: '张三三',
            school: '中国科学院大学',
            degree: '硕士',
            totalPages: 50,
            chineseChars: 80096,
            englishChars: 396,
            wordCount: 80378,
          }"
        />
      </div>

      <!-- Test Report Section -->
      <div class="mb-8">
        <ListHeader
          chinese="检测报告"
          english="TEST REPORT"
        />

        <div class="w-full flex flex-row justify-between items-center my-6">
          <div class="flex-1 flex items-center gap-9">
            <span class="label">格式错误总数</span>
            <span class="content">{{ paperInfo?.totalPages || '50' }}页</span>
          </div>
          <div class="flex-1 flex items-center gap-9">
            <span class="label">建议修改总数</span>
            <span class="content">{{ paperInfo?.wordCount || '80,378' }}字</span>
          </div>
        </div>

        <Table>
          <template #header>
            <div class="w-full h-full flex items-center ">
              <div class="flex flex-row items-center gap-4 ">
                <span class="text-[16px] !text-black">统计总表</span>
              </div>
            </div>
          </template>
        </Table>
        <div class="w-full h-6" />
        <Table>
          <template #header>
            <div class="w-full h-full flex items-center ">
              <div class="flex flex-row items-center gap-4 ">
                <span class="text-[16px] !text-black">摘要</span>
                <span class="text-sm font-normal text-gray-500" />
              </div>
            </div>
          </template>
        </Table>
      </div>

      <!-- 报告说明 -->
      <div class="mb-8">
        <ListHeader
          chinese="报告说明"
          english="REPORT DESCRIPTION"
        />

        <ReportInfo :report-info="reportInfo" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.label {
  font-size: 14px;
  width: 84px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
}
.content {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}
</style>