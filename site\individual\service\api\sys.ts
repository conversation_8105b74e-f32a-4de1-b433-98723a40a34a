import { ElMessage } from 'element-plus'
import { request } from '../request'

export interface UploadPaperData {
  file: File
  author: string
  school: string
  titleCn: string
  titleEN: string
  degree: string
}

export function uploadPaper(data: FormData) {
  return request({
    url: '/review/uploadPaper',
    method: 'post',
    data,
  })
}

export function uploadPdf(data: FormData) {
  return request({
    url: '/review/uploadPdf',
    method: 'post',
    data,
  })
}

export function getPaperInfo(data: object) {
  return request({
    url: '/review/getCoverResult',
    method: 'get',
    params: data,
  })
}

export function paperList(data: any) {
  return request({
    url: '/review/paperList',
    method: 'post',
    data,
  })
}

export function deletePaperDetail(data: any) {
  return request({
    url: '/review/deletePaperDetail',
    method: 'post',
    data,
  })
}

export function saveBasicInfo(data: any) {
  return request({
    url: '/review/saveBasicInfo',
    method: 'post',
    data,
  })
}

export function paperDownload(data: any) {
  try {
    return request({
      responseType: 'blob',
      url: '/review/download',
      method: 'post',
      data,
    })
  }
  catch (e) {
    ElMessage({
      type: 'error',
      message: '下载文件失败,请稍后重试',
    })
  }
}

export function getPaperDetail(data: any) {
  return request({
    url: '/review/getPaperDetail',
    method: 'post',
    data,
  })
}
/**
 * 学生提交给教师审核，会发送邮件给教师
 * @param data 
 * @returns 
 */
export function sendEmailReviewApi(data?: any) {
  return request({
    url: '/review/sendEmailReview',
    method: 'post',
    data,
  })
}

/**
 * 获取教师列表
 * @param data 
 * @returns 
 */
export function getTeacherInfoApi(data?: any) {
  return request({
    url: '/review/getTeacherInfo',
    method: 'post',
    data,
  })
}

/**
 * 获取论文审核通知
 * @param data 
 * @returns 
 */
export function getPaperReviewNoticeApi(data?: any) {
  return request({
    url: '/review/getPaperReviewNotice',
    method: 'post',
    data,
  })
}

export function submitFeedBack(data?: any) {
  return request({
    url: '/review/submitFeedback',
    method: 'post',
    data,
  })
}

export function getManualCheckPaperList(data?: any) {
  return request({
    url: '/review/getTemplatePaperReviewRecord',
    method: 'post',
    data,
  })
}

export function updateTemplatePaperReviewRecord(data?: any) {
  return request({
    url: '/review/updateTemplatePaperReviewRecord',
    method: 'post',
    data,
  })
}