<script setup lang="ts">
import TimeLine from './customTimeline.vue'

const props = defineProps<{
  loading: boolean
  uploaded: boolean
  dragOver: boolean
  uploadCheckInfo: any
  currentFile: any
}>()

watch(() => props.uploadCheckInfo, (val) => {
  // console.log('this is val:', val)
  // const { zipStatus, zipErrorMessage, taskId } = val
})
const typeName = ref('')
watch(() => props.currentFile, (val) => {
  if (!val) 
    return typeName.value = ''
  typeName.value = val.name.split('.').pop() === 'zip' ? 'Zip' : 'Word'
})

const { setStatus } = inject('useStatus') as any
const STATUS = inject('STATUS') as any

const status = computed(() => {
  if (props.loading) {
    if (props.uploaded) 
      return 2
    else 
      return 1
  }
  else if (!props.loading) {
    if (props.uploaded) 
      return 2
  }
  else if (props.uploadCheckInfo && props.uploadCheckInfo.taskId) {
    return 2
  }
  return 0
})
const showFlag = computed(() => {
  if ((props.uploadCheckInfo && props.uploadCheckInfo.taskId) || status.value > 0) {
    return 2
  }
  return 1
})
</script>

<template>
  <div v-show="!dragOver" class="box-border w-[40%]">
    <div v-show="showFlag === 1">
      <div class="text-[24px] font-bold mb-[3.2rem]">
        注意事项
      </div>
      <div class="text-[14px] mb-[1rem]">
        为确保检测的准确性，请您提交论文的全部内容
      </div>
      <div class="text-[14px] mb-[1rem]">
        支持检测的文件类型为<span class="text-[#d10000]">Word</span>和<span class="text-[#d10000]">Latex</span>
      </div>
      <div class="text-[14px] mb-[1rem]">
        支持扩展名为<span class="text-[#d10000]">.docx</span>和<span class="text-[#d10000]">.zip</span>的文件
      </div>
      <div class="text-[14px] mb-[1rem]">
        上传文件最大限制为500MB
      </div>
      <div class="flex flex-nowrap">
        <img src="@individual/assets/svg/word_new.svg" class="mr-[10px]">
        <img src="@individual/assets/svg/ZIP-2.svg" class="mr-[10px]">
      </div>
    </div>

    <div v-show="showFlag === 2">
      <div class="text-[24px] font-bold mb-[3.2rem]">
        解析进度
      </div>
      <TimeLine 
        :type-name="typeName"
        :loading="loading"
        :uploaded="uploaded" 
        :drag-over="dragOver" 
        :upload-check-info="uploadCheckInfo"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.timeline-container {
  :deep(.el-timeline-item__dot) {
    left: -5px !important;
    top: -8px !important;
  }
  :deep(.el-timeline-item__wrapper) {
    top: -8px;
  }
}
.bgline {
  background: #d1fcd7;
}
</style>