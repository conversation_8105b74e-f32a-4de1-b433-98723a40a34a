import type { App } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'
import { createRouterGuard } from './guard'
import { basicRoutes } from './routes/index'

export const router = createRouter({
  history: createWebHashHistory(),
  routes: basicRoutes as RouteRecordRaw[],
  strict: true,
})

export function setupRouter(app: App) {
  app.use(router)
  createRouterGuard(router)
}