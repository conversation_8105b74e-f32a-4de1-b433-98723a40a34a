<script setup>
import LeftPoster from './components/left-poster.vue'
import RightPanel from './components/right-panel.vue'

const containerClass = cn('min-w-[1440px] w-full h-full relative overflow-hidden bg-[rgba(55,204,176,0.20)] flex flex-nowrap'.split(' '))
</script>

<template>
  <div :class="containerClass" class="login-outer-box">
    <LeftPoster />
    <RightPanel />
  </div>
</template>

<style>
.login-outer-box .el-input__wrapper.is-focus {
  position: relative;
  box-shadow: none;
}
.login-outer-box .el-input__wrapper.is-focus::before {
  content: '';
  position: absolute;
  width: 101%;
  height: 101%;
  border-radius: 8px;
  z-index: -1;
  border: 2px solid transparent;
  border-radius: 8px;
  background-clip: content-box, border-box;
  background-origin: content-box, border-box;
  background-image: linear-gradient(to right, #fff, #fff), linear-gradient(45deg, #82df67, #06b190);
}
</style>
