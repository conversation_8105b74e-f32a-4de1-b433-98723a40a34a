import type { Router } from 'vue-router'
import { localStg, sessionStg } from '@paper-review/utils'
import { verifyTeacherTokenApi, verifyToken } from '../../service/api'

export async function redirectRoute(router) {
  const passhash = [
    '#/policy', 
    '#/privacy', 
    '#/404',
    '#/teacher-check-operate',
  ]
  /** 1. 白名单 */
  const hash = window.location.hash
  if (passhash.includes(hash)) 
    return

  /** 2. 教师端携带 code 判断是否有效 */
  const params = useUrlSearchParams('hash')

  if (params?.code) {
    try {
      const res = await verifyTeacherTokenApi({ code: params.code })
      const { 
        code, /** 200 有效 | 500 失效 */
        msg,
        data,
      } = res.data 

      if (code === 200) {
        localStg.set('token', data.token)
      }
      else {
        console.error(msg)
      }
    }
    catch (error) {
      console.error(error)
    }
  }

  /** 3. 学生端 token 是否有效 */
  else {
    const res = await verifyToken()
    const { code /** 200 有效 | 500 失效 */ } = res.data 
    sessionStg.set('tokenValidCode', code)
    const userInfo = localStg.get('userInfo')
    const token = sessionStg.get('token') || localStg.get('token') || ''
    const tokenValid = code === 200
  
    if (code !== 200) {
      localStg.remove('token')
      sessionStg.remove('token')
    }

    if (userInfo && token && tokenValid) 
      return

    if (!userInfo && !token) {
      return router.push({ name: 'review' })
    }
  
    else if (userInfo && (!token || !tokenValid)) { 
      return router.push({ name: 'login' })
    }
  }
}

export function createRouteGuard(router: Router) {
  redirectRoute(router)
  router.beforeEach((to, from, next) => {
    next()
  })
}

export function createRouterGuard(router: Router) {
  createRouteGuard(router)
}
