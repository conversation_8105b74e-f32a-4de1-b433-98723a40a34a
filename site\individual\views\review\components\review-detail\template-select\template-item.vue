<script setup lang="ts">
const emits = defineEmits(['selctTemplate'])
const checked = ref(false)
function clickTempate() {
  checked.value = !checked.value
  emits('selctTemplate', checked.value)
}
</script>

<template>
  <div :class="{ 'template-select': checked }" class="select-none box-border w-[248px] h-[272px] p-[18px] bg-[#f4f6f6] border-[1px] border-[#f4f6f6] rounded-[8px] flex justify-center relative" @click="clickTempate">
    <div v-show="checked" class="absolute w-[24px] h-[24px] rounded-[8px] bg-[#06B190] top-[-1px] right-[-1px]">
      <img src="@individual/assets/svg/template-check.svg" class="w-[24px] h-[24px]">
    </div>
    <img src="@individual/assets/svg/editTemplate.svg" class="w-full object-contain ">
    <div class="box-border absolute w-full h-full p-[18px] left-0 top-0 flex flex-wrap justify-center content-start">
      <div class="w-full h-[44%] flex flex-wrap justify-center">
        <!-- <span class="degree-ben absolute text-[12px]" style="writing-mode: vertical-rl;letter-spacing: 2px;">硕士</span> -->
        <img class="w-[30%] object-contain" src="@individual/assets/image/school-logo.jpg">
      </div>
      <div class="w-full box-border h-[30px] flex flex-wrap justify-center">
        <div class="base:text-[16px] s-base:text-[14px] px-[12px] font-[600] w-full text-center">
          中国科学院大学
        </div>
        <div class="base:text-[13px] s-base:text-[12px] w-full p-[50px] text-center top-[72%] translate-y-[-20%]">
          所有学科
        </div> 
      </div>
    </div>
  </div>
</template>

<style scoped>
.degree::before {
  display: inline-block;
  content: '111';
}
.degree-ben {
  position: absolute;
  top: 18px;
  left: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 58px;
  background-size: 100% 100%;
  background-image: url('../../../../../assets/svg/master.svg');
}
.template-select {
  border-radius: 8px;
  background: #eaf9f9;
  border: 1px solid #06b190;
}
</style>