<script lang="ts" setup>
import type { DialogProps } from 'element-plus/es/components/dialog'
import { ElDialog } from 'element-plus'

const props = defineProps<
  Partial<DialogProps> &
  {
    title?: any
    width?: number | string
  }
>()
  
const dialogVisible = defineModel('dialogVisible', {
  type: Boolean,
  default: false,
})

function handleBeforeClose(done: () => void) {
  done()
}
</script>

<template>
  <ElDialog
    v-bind="props"
    v-model="dialogVisible"
    :width="width"
    align-center
    :show-close="false"
    destroy-on-close
    :before-close="handleBeforeClose"
    :style="{
      'min-width': '200px',
      '--el-dialog-padding-primary': '0px',
      'border-radius': '8px',
      'overflow': 'hidden',
      'padding': '32px',
    }"
  >
    <template #header="{ close }">
      <div class="w-full leading-[22px] text-[20px] flex justify-between items-center">
        <span>
          {{ title }}
        </span>

        <div>
          <svg
            aria-label="关闭按钮"
            class="w-[20px] h-[20px] cursor-pointer hover:text-active"
            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" @click="close"
          >
            <path
              fill="currentColor"
              d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"
            />
          </svg>
        </div>
      </div>
    </template>

    <div class="w-full min-h-[200px] pt-[32px]">
      <slot />
    </div>

    <div 
      aria-label="footer"
      class="mt-[32px] flex flex-row justify-center items-center"
    >
      <slot name="footer" />
    </div>
  </ElDialog>
</template>
