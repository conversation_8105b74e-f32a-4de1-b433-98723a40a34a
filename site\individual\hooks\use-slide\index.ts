const showSlide = ref(true)
const verifyCodeMsg = ref('获取验证码')
const initInerval = 60
const verifyCodeTime = ref(initInerval)
const disabledSendCode = ref(false)
let pauseFunc = null as any

export function slideToSended(cleanup?: () => void) {
  showSlide.value = false
  disabledSendCode.value = true
  const { pause } = useIntervalFn(() => {
    verifyCodeMsg.value = `重新发送 ${verifyCodeTime.value--}s`
  }, 1000, { immediateCallback: true })
  pauseFunc = pause
  useTimeoutFn(() => {
    pause()
    verifyCodeTime.value = initInerval
    verifyCodeMsg.value = `获取验证码`
    disabledSendCode.value = false
  }, initInerval * 1000)
  nextTick(() => {
    cleanup && cleanup()
  })
}
export function cleanSlide() {
  showSlide.value = true
  verifyCodeMsg.value = `获取验证码`
  verifyCodeTime.value = initInerval
  pauseFunc && pauseFunc()
}

export function useSlide() {
  return { showSlide, verifyCodeMsg, disabledSendCode, cleanSlide, slideToSended }
}