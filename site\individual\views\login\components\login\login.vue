<script setup lang="ts">
import PwdLogin from './pwd-login.vue'
import VerificationCodeLogin from './validate-code-login.vue'

enum LOGINTYPE {
  PASSWORD = 'password',
  VERIFYCODE = 'verificationCode',
}
const loginType = ref<LOGINTYPE>(LOGINTYPE.VERIFYCODE)

function changeLoginType(type: LOGINTYPE) {
  loginType.value = type
}
</script>

<template>
  <div class="w-[40%] bg-[rgba(255,255,255,0.70)] overflow-hidden flex justify-center content-start relative flex-wrap base:pt-[195px] s-base:pt-[135px]">
    <div class="w-[380px] relative flex flex-wrap justify-start">
      <div class="w-full flex flex-nowrap">
        <!-- <div class="tab-item mr-[4.8rem]" :class="{ 'tab-item-active': loginType === LOGINTYPE.PASSWORD }" @click="changeLoginType(LOGINTYPE.PASSWORD)">
        密码登录
      </div> -->
        <!-- :class="{ 'tab-item-active': loginType === LOGINTYPE.VERIFYCODE }"  -->
        <div class="tab-item" @click="changeLoginType(LOGINTYPE.VERIFYCODE)">
          验证码登录
        </div>
      </div>

      <PwdLogin v-if="loginType === LOGINTYPE.PASSWORD" />
      <VerificationCodeLogin v-if="loginType === LOGINTYPE.VERIFYCODE" />
      <div />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tab-item {
  cursor: pointer;
  box-sizing: border-box;
  font-size: 24px;
  font-family:
    PingFang SC,
    PingFang SC-Medium;
  font-weight: Medium;
  text-align: left;
  color: rgba(0, 0, 0, 0.85);
}
.tab-item-active {
  cursor: pointer;
  box-sizing: border-box;
  font-size: 24px;
  line-height: 20px;
  position: relative;
  color: #06b190;
  &::after {
    position: absolute;

    height: 2px;
    box-sizing: border-box;
    width: 100%;
    content: '';
    display: inline-block;
    background-color: #06b190;
    top: 160%;
    left: 0;
  }
}
:deep(.el-input__wrapper) {
  border-radius: 8px;
}
</style>