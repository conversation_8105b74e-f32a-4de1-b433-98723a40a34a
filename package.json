{"name": "t", "type": "module", "version": "0.0.0", "private": true, "scripts": {"cz": "git add .&& git cz", "mock-server": "pnpm -C packages/mock dev", "dev": "vite --host --mode individual", "dev:teacher": "vite --host --port 8081 --mode individual", "build:individual": "vite build --mode individual", "lint": "eslint .", "lint:fix": "eslint --fix", "commitlint": "commitlint --config commitlint.config.js -e"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@emotion/css": "^11.13.5", "@site/individual": "workspace:*", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vueuse/core": "^12.0.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "element-plus": "^2.9.1", "eslint-plugin-file-progress": "^3.0.1", "file-size": "^1.0.0", "file-type": "^19.6.0", "lint-staged": "^15.2.11", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "nanoid": "^5.0.9", "pinia": "^2.3.0", "qs": "^6.13.1", "simplex-noise": "^4.0.3", "slide-verify": "^1.0.9", "vue": "^3.5.13", "vue-router": "^4.5.0", "wangeditor": "^4.7.15"}, "devDependencies": {"@antfu/eslint-config": "^3.12.0", "@arvinn/vscode-settings": "^0.0.3", "@commitlint/cli": "^19.6.1", "@iconify/vue": "^4.3.0", "@paper-review/axios": "workspace:*", "@paper-review/components": "workspace:*", "@paper-review/mock": "workspace:*", "@paper-review/utils": "workspace:*", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cz-git": "^1.11.0", "eslint": "^9.17.0", "eslint-plugin-format": "^0.1.3", "lucide-vue-next": "^0.473.0", "motion-v": "^1.6.1", "postcss": "^8.4.49", "sass-embedded": "^1.83.0", "simple-git-hooks": "^2.11.1", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "unplugin-auto-import": "^0.19.0", "vite": "^6.0.1", "vue-tsc": "^2.1.10"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "simple-git-hooks": {"commit-msg": "pnpm commitlint", "pre-commit": "pnpm lint-staged"}}