<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/40文件/tex</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="✅论文检测" transform="translate(-296.000000, -555.000000)">
            <g id="编组-3" transform="translate(140.000000, 281.000000)">
                <g id="icon/40文件/tex" transform="translate(156.000000, 274.000000)">
                    <rect id="_mockplus_fix_" x="0" y="0" width="40" height="40"></rect>
                    <rect id="矩形" fill="#000000" fill-rule="nonzero" opacity="0" x="0" y="0" width="40" height="40"></rect>
                    <path d="M8,0 L26.6666797,0 L37.3333203,10.6666797 L37.3333203,34.6666797 C37.3333203,37.6 34.9333203,40 32,40 L8,40 C5.06667969,40 2.66667969,37.6 2.66667969,34.6666797 L2.66667969,5.33332031 C2.66667969,2.4 5.06667969,0 8,0 Z" id="形状" fill="#BC8585" fill-rule="nonzero"></path>
                    <path d="M11,0 L11,16 L17.75,16 L17.75,0 L11,0 Z M16.875,5.5 L14.375,5.5 L14.375,7.125 L16.875,7.125 L16.875,8.875 L14.375,8.875 L14.375,10.5 L16.875,10.5 L16.875,12.25 L14.375,12.25 L14.375,14 L11.875,14 L11.875,12.25 L14.375,12.25 L14.375,10.5 L11.875,10.5 L11.875,8.875 L14.375,8.875 L14.375,7.125 L11.875,7.125 L11.875,5.5 L14.375,5.5 L14.375,3.875 L11.875,3.875 L11.875,2.125 L14.375,2.125 L14.375,3.875 L16.875,3.875 L16.875,5.5 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
                    <text id="ZIP" font-family="PingFangSC-Medium, PingFang SC" font-size="12" font-weight="400" line-spacing="18" letter-spacing="0.5" fill="#FFFFFF">
                        <tspan x="10" y="33">ZIP</tspan>
                    </text>
                    <path d="M26.6666797,0 L37.3333203,10.6666797 L29.3333203,10.6666797 C27.7333203,10.6666797 26.6666797,9.6 26.6666797,8 L26.6666797,0 Z" id="路径" fill="#F5ECEC" fill-rule="nonzero"></path>
                </g>
            </g>
        </g>
    </g>
</svg>