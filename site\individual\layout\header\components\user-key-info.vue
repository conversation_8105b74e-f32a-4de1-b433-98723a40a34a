<script setup lang="ts">
import { CaretBottom } from '@element-plus/icons-vue'
import { useUserStore } from '../../../store/modules/user.ts'

const userStore = useUserStore()
</script>

<template>
  <div class="w-fit h-full flex items-center pr-[24px]">
    <div class="content-center align-top flex flex-nowrap items-center">
      <img class="w-[32px] h-[32px] rounded-full mr-[12px]" src="../../../assets/svg/avatar.svg" alt="avatar">
      <span class="text-[14px] mr-[1rem]" style="vertical-align:text-top;">{{ userStore.userInfo.username }}</span>
      <!-- <el-tag type="success">
        清华大学
      </el-tag> -->
    </div>
    
    <el-popover
      placement="bottom"
      :width="100"
      trigger="click"
    >
      <template #reference>
        <CaretBottom class="w-[16px] h[-16px] ml-[1.6rem] cursor-pointer" />
      </template>
      <div class="w-full relative">
        <div class="cursor-pointer hover:bg-[#D2FCD6] w-full flex justify-center py-[6px] rounded-[6px]" @click="userStore.logout">
          退出登录
        </div>
      </div>
    </el-popover>
  </div>
</template>
