<script lang="ts" setup>
import type { TableColumnCtx } from 'element-plus/es/components/table'
import { ChevronUp } from 'lucide-vue-next'

const props = defineProps<{
  type: 'abstract' | 'summary' | 'history' | 'semantic'
  tableData?: any
}>()
const reportDetail = inject('reportDetail') as any
const tableData = ref([])
const tableColumn = ref<any>([])
/** 定义表格列 */
// const abstractTableColumn: Partial<TableColumnCtx<any>>[] = [
//   { label: '序号', prop: 'serialNo', showOverflowTooltip: true },
//   { label: '问题类型', prop: 'errorType', sortable: false, align: 'left' },
//   { label: '检测项', prop: 'checkitem', sortable: false, align: 'left' },
//   { label: '页码', prop: 'pageNumber', sortable: false, align: 'left' },
//   { label: '描述', prop: 'problemDescription', showOverflowTooltip: true, sortable: false, align: 'center', width: 550 },
// ]

// const summaryTableColumn: Partial<TableColumnCtx<any>>[] = [
//   { label: '序号', prop: 'serialNo', showOverflowTooltip: true },
//   { label: '章节', prop: 'chapter', sortable: false, align: 'left' },
//   { label: '页码范围', prop: 'pageRange', sortable: false, align: 'left' },
//   { label: '格式错误', prop: 'formatError', sortable: false, align: 'left' },
//   { label: '语义偏差', prop: 'semanticDeviation', showOverflowTooltip: true, sortable: false, align: 'left' },
//   { label: '优化建议', prop: 'improvementSuggestions', showOverflowTooltip: true, sortable: false, align: 'left' },
// ]
const abstractTableColumn: Partial<TableColumnCtx<any>>[] = [
  { label: '序号', prop: 'serialNo', showOverflowTooltip: true },
  { label: '问题描述', prop: 'problemDescription', sortable: false, align: 'left' },
  { label: '原文内容举例', prop: 'originalText', showOverflowTooltip: false, sortable: false, align: 'left', width: 550 },
  { label: '对应页码', prop: 'pageNumber', sortable: false, align: 'left' },
]
const summaryTableColumn: Partial<TableColumnCtx<any>>[] = [
  { label: '序号', prop: 'serialNo', showOverflowTooltip: true },
  { label: '问题描述', prop: 'problemDescription', sortable: false, align: 'left' },
  { label: '原文内容举例', prop: 'originalText', showOverflowTooltip: false, sortable: false, align: 'left', width: 550 },
  { label: '对应页码', prop: 'pageNumber', sortable: false, align: 'left' },
]
const semanticTableColumn: Partial<TableColumnCtx<any>>[] = [
  { label: '序号', prop: 'serialNo', showOverflowTooltip: true },
  { label: '问题描述', prop: 'problemDescription', sortable: false, align: 'left' },
  { label: '原文内容举例', prop: 'originalText', showOverflowTooltip: false, sortable: false, align: 'left', width: 550 },
  { label: '对应页码', prop: 'pageNumber', sortable: false, align: 'left' },
]
const historyTableColumn: Partial<TableColumnCtx<any>>[] = [
  { label: '任务名称', prop: 'taskName', showOverflowTooltip: true },
  { label: '格式错误', prop: 'formatError', sortable: false, align: 'left' },
  { label: '语义偏差', prop: 'semanticDeviation', sortable: false, align: 'left' },
  { label: '优化建议', prop: 'improvementSuggestions', sortable: false, align: 'left' },
  { label: '提交时间', prop: 'createTime', sortable: true, align: 'left' },
]

/** 展开 */
const isExpand = ref('1')

watch(() => reportDetail.value, (val) => {
  if (!val) 
    return
  if (props.type === 'abstract') {
    tableColumn.value = abstractTableColumn
    tableData.value = val.reviewResultTemplate.abstractList.filter(item => item.errorType === '优化建议').filter(item => item.originalText || item.pageNumber).map((item, index) => {
      return item
    })
  }
  else if (props.type === 'summary') {
    // 格式错误
    tableColumn.value = summaryTableColumn
    tableData.value = val.reviewResultTemplate.abstractList.filter(item => item.errorType === '格式错误').filter(item => item.originalText || item.pageNumber).map((item, index) => {
      return item
    })
  }
  else if (props.type === 'semantic') {
    tableColumn.value = semanticTableColumn
    tableData.value = val.reviewResultTemplate.abstractList.filter(item => item.errorType === '语义偏差').filter(item => item.originalText || item.pageNumber).map((item, index) => {
      return item
    })
  }
  else if (props.type === 'history') {
    tableColumn.value = historyTableColumn
    tableData.value = props.tableData
  }
})
/**
 * el-table row 样式
 */
function tableRowClassName({ rowIndex }: { rowIndex: number }) {
  if (rowIndex % 2 === 0) {
    return ''
  }
  else {
    return '!bg-[#F8FAFE]'
  }
}
</script>

<template>
  <div class="flex flex-col ">
    <el-collapse
      v-model="isExpand"
      accordion
      style="--el-collapse-border-color: transparent"
    >
      <el-collapse-item name="1">
        <template #icon>
          <div class="w-0" />
        </template>
        <template #title>
          <div 
            class="
              w-full h-[42px] px-[18px] 
              bg-[#f2f4f8] rounded-t-lg overflow-hidden 
              flex items-center gap-4
              transition-all duration-300 ease-in-out
            "
            :class="{
              'rounded-b-lg': isExpand !== '1',
            }"
          >
            <ChevronUp 
              class="w-4 h-4 cursor-pointer transition-all duration-300 ease-in-out" 
              :class="{
                'rotate-180': isExpand === '1',
              }"
            />
          
            <slot name="header" />
          </div>
        </template>

        <el-table 
          :show-header="true"
          :data="tableData" 
          style="width: 100%"
          stripe
          :row-class-name="tableRowClassName"
          :header-cell-style="{
            color: '#000000',
            fontWeight: '500',
          }"
        >
          <el-table-column 
            v-for="(item, index) in tableColumn" 
            :key="index"
            :width="item.prop === 'serialNo' ? '60px' : item.width"
            v-bind="item"
          >
            <template #default="{ row }">
              <span>{{ row[item.prop] || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
