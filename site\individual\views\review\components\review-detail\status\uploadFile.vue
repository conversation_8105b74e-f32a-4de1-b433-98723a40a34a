<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import WORD from '@individual/assets/svg/word_new.svg'
import ZIP from '@individual/assets/svg/zip_new.svg'
import { getFileSize, getFileType } from '@paper-review/utils'
import PaperUpload from './upload/PaperSuffixUploader.vue'

const props = defineProps<{
  dragOver: boolean
  loading: boolean
  uploaded: boolean
  uploadCheckInfo: any
  currentFile: any
}>()
const emits = defineEmits([
  'onExceed',
  'onBeforeUpload',
  'uploadFile',
  'dragover',
  'dragleave',
])
const zipErrorMessageRecord = ref('')
const taskIdRecord = ref('')
watch(
  () => props.uploadCheckInfo,
  (val) => {
    const { zipStatus, zipErrorMessage, taskId } = val
    zipErrorMessageRecord.value = zipErrorMessage
    taskIdRecord.value = taskId
  },
)

const { setStatus } = inject('useStatus') as any
const STATUS = inject('STATUS') as any

const dragOver = ref(false)
const uploadFileRef = useTemplateRef<any>('uploadFileRef')

function dragover(e: DragEvent) {
  console.log('this is dragover')
  emits('dragover', e)
}
function dragleave(e: DragEvent) {
  console.log('this is drageLEave')
  emits('dragleave', e)
}
function onExceed() {
  emits('onExceed')
}
function onBeforeUpload(file: File, type?: string) {
  emits('onBeforeUpload', file, type)
}
function uploadFile(options, taskId?) {
  zipErrorMessageRecord.value = ''
  emits('uploadFile', options, taskId)
}
function clearUploadState() {
  uploadFileRef.value?.clearFiles()
}
async function handleFileSelected(file: File, type?: string) {
  console.log('this is file', file)
  const defaultTypes = ['docx', 'zip']
  const checkFileType = await getFileType(file.stream())
  const checkSuffix = file.name.split('.').pop() || ''
  const checkFileTypeInValid
    = !checkFileType || !defaultTypes.includes(checkFileType?.ext)
  const checkSuffixInValid = !defaultTypes.includes(checkSuffix)
  if (checkFileTypeInValid || checkSuffixInValid) {
    return emits('onBeforeUpload', file, type)
  }
  else if (getFileSize(file.size) > 500) {
    return emits('onBeforeUpload', file, type)
  }
  uploadFile({ file })
}
async function handleFileRejected(file: File, type?: string) {
  const defaultTypes = ['docx', 'zip']
  const checkFileType = await getFileType(file.stream())
  const checkSuffix = file.name.split('.').pop() || ''
  const checkFileTypeInValid
    = !checkFileType || !defaultTypes.includes(checkFileType?.ext)
  const checkSuffixInValid = !defaultTypes.includes(checkSuffix)
  if (checkFileTypeInValid || checkSuffixInValid) {
    return emits('onBeforeUpload', file, type)
  }
  else if (getFileSize(file.size) > 500) {
    return emits('onBeforeUpload', file, type)
  }
}
defineExpose({
  clearFiles: () => uploadFileRef.value.clearFiles(),
  clearUploadState,
})
</script>

<template>
  <div
    :class="`rounded-[8px] box-border w-[${
      props.dragOver ? '100%' : '480px'
    }] h-[304px] relative`"
    @dragover.prevent="dragover"
    @dragleave.prevent="dragleave"
  >
    <PaperUpload
      :on-exceed="onExceed"
      @handle-file-selected="handleFileSelected"
      @handle-file-rejected="handleFileRejected"
      @drag-enter="dragover"
      @drag-leave="dragleave"
    />

    <el-upload
      v-if="parseInt(zipErrorMessageRecord) === 1"
      ref="uploadFileRef"
      v-loading="loading"
      class="paper-upload-file-PDF"
      drag
      accept=".pdf"
      :limit="1"
      :on-exceed="onExceed"
      :before-upload="(file) => onBeforeUpload(file, 'pdf')"
      :show-file-list="false"
      :http-request="(options) => uploadFile(options, taskIdRecord)"
    >
      <div
        :class="`${
          props.dragOver ? 'ml-[50%] translate-x-[-50%]' : ''
        } upload-container  el-upload__text flex flex-wrap items-center mt-[10%] relative w-[480px]`"
        style="pointer-events: none"
      >
        <img
          src="@individual/assets/svg/pdf1.svg"
          class="absolute top-[-10px] left-[50px]"
        >
        <img
          src="@individual/assets/svg/pdf2.svg"
          class="absolute top-[-30px] right-[10px]"
        >

        <div
          class="w-fit min-w-[128px] bg-line justify-center items-center mb-[21px] mt-[20px] relative"
          style="width: fit-content"
        >
          <el-icon class="text-[10px] mr-[10px]">
            <Plus color="#ffffff" />
          </el-icon>
          <span>上传PDF</span>
        </div>
        <div class="text-[17px] mb-[5px] w-full text-[#FF4D4F] font-bold">
          未匹配到对应的PDF文件
        </div>
        <div class="text-[15px] mb-[5px] w-full">
          请点击或拖拽上传对应的PDF文件
        </div>
        <div v-show="!dragOver" class="w-full text-[15px]">
          也可以选择<span class="text-[#06B190]">点击上传</span>
        </div>
      </div>
    </el-upload>
    <el-upload
      v-else-if="!uploaded && !loading"
      ref="uploadFileRef"
      class="paper-upload-file"
      drag
      accept=".docx,.zip"
      :limit="1"
      :on-exceed="onExceed"
      :before-upload="onBeforeUpload"
      :show-file-list="false"
      :http-request="uploadFile"
    >
      <div
        :class="`${
          props.dragOver ? 'ml-[50%] translate-x-[-50%]' : ''
        } upload-container  el-upload__text flex flex-wrap items-center mt-[10%] relative w-[480px]`"
        style="pointer-events: none"
      >
        <img
          src="@individual/assets/svg/80word.svg"
          class="absolute top-[-70px] left-[30px]"
        >
        <img
          src="@individual/assets/svg/ZIP-2-BIG.svg"
          class="absolute right-[20px] bottom-[-90px]"
        >

        <div
          class="btn w-fit min-w-[80px] bg-line justify-center items-center mb-[11px] mt-[20px] relative"
          style="width: fit-content"
        >
          <el-icon class="select-icon">
            <Plus color="#ffffff" />
          </el-icon>
          <span class="select-text">选择文件</span>
        </div>
        <div class="text-[17px] mb-[10px] w-full normal-text">
          {{ props.dragOver ? "松手添加论文至此处" : "  将论文拖拽到此处" }}
        </div>
        <div class="text-[17px] mb-[10px] w-full hover-text">
          Word源文件、Latex打包的Zip文件
        </div>
        <div v-show="!dragOver" class="w-full normal-text-bottom">
          或直接<span class="text-[#06B190]">点击上传</span>
        </div>
      </div>
    </el-upload>
    <div
      v-else
      class="w-full h-full bg-red rounded-[8px] flex flex-wrap justify-center content-center bg-[#fff] border-[1px] border-dashed border-[#11c4a2]"
    >
      <img
        :src="currentFile && currentFile.name.split('.').pop() === 'zip' ? ZIP as any : WORD as any "
        class="w-[80px]"
      >
      <div
        class="w-full flex justify-center text-[rgba(0,0,0,0.65)] font-[14px] mt-[20px]"
      >
        <span> 正在努力解析中，请耐心等待… </span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.hover-text {
  display: none;
}

.select-text {
  display: none;
}

.select-icon {
  font-size: 20px;
  font-weight: bolder;
}
.btn {
  width: 300px;
}
.paper-upload-file:hover {
  .normal-text {
    display: none;
  }
  .normal-text-bottom {
    visibility: hidden;
  }
  .hover-text {
    display: block;
  }
  .btn {
    min-width: 128px;
  }
  .select-icon {
    font-size: 18px;
  }
  .select-text {
    display: inline;
    margin-top: 2px;
    margin-left: 10px;
  }
}
:deep(.el-upload-dragger) {
  height: 320px;
}
.bg-line {
  margin-left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  color: #fff;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(90deg, #82df67, #06b190);
}
</style>
