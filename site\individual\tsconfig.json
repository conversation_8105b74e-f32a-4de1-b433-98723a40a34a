{
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "jsx": "preserve",
    "jsxImportSource": "vue",

    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "moduleDetection": "force",
    "useDefineForClassFields": true,
    "module": "ESNext",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "typeRoots": ["./@types", "../../src/typings"],
    "allowImportingTsExtensions": true,

    /* Linting */
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": false,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    "isolatedModules": true,
    "skipLibCheck": true,
    "noUncheckedSideEffectImports": true
  },
  "include": [
    "./@types/**/*.ts",
    "./**/*.ts",
    "./**/*.vue",
    "views/review/components/slogan.tsx",
    "views/review/components/review-detail/status/index.tsx",
    "views/review/components/review-detail/status/feedback.tsx",
    "views/record/table-record.tsx"
  ]
}
