export const checkList = [
  {
    serialNo: 1,
    format: '全文',
    type: '格式错误',
    checkDesc: '论文应包含以下章节：封面、中英文摘要、目录、图表目录、正文、参考文献',
    problemDescription: '论文内容不完整，请检查是否缺失以下章节：封面、中英文摘要、目录、图表目录、正文、参考文献',
  },
  {
    serialNo: 2,
    format: '全文',
    type: '格式错误',
    checkDesc: '章节内容不应为空',
    problemDescription: '章节内容为空，建议补全',
  },
  {
    serialNo: 3,
    format: '全文',
    type: '优化建议',
    checkDesc: '论文总字数应达到8万字',
    problemDescription: '论文总字数未达到8万字',
  },
  {
    serialNo: 4,
    format: '全文',
    type: '格式错误',
    checkDesc: '论文不应存在空白页',
    problemDescription: '论文出现排版错误，请检查是否存在空白页',
  },
  {
    serialNo: 5,
    format: '全文',
    type: '格式错误',
    checkDesc: '「W」标题格式应符合模板要求',
    problemDescription: '标题格式错误，请检查：字体字号、加粗、斜体、标题等级等',
  },
  {
    serialNo: 6,
    format: '全文',
    type: '格式错误',
    checkDesc: '「W」文字内容格式应符合模板要求',
    problemDescription: '文字内容格式错误，请检查：字体字号、加粗、斜体等',
  },
  {
    serialNo: 7,
    format: '全文',
    type: '格式错误',
    checkDesc: '「W」题注格式应符合模板要求',
    problemDescription: '题注格式错误，请检查：字体字号、加粗、斜体等',
  },
  {
    serialNo: 8,
    format: '全文',
    type: '格式错误',
    checkDesc: '「W」序号格式应符合模板要求',
    problemDescription: '序号格式错误，请检查：字体字号、加粗、斜体等',
  },
  {
    serialNo: 9,
    format: '全文',
    type: '格式错误',
    checkDesc: '「W」页面配置应符合模板要求',
    problemDescription: '页面配置错误，请检查：页边距、大小和方向等',
  },
  {
    serialNo: 10,
    format: '全文',
    type: '格式错误',
    checkDesc: '页眉页脚应符合模板要求',
    problemDescription: '页眉页脚错误或内容缺失',
  },
  {
    serialNo: 11,
    format: '全文',
    type: '格式错误',
    checkDesc: '页眉内容应与当前内容的一级标题相符',
    problemDescription: '页眉内容与当前章节不符',
  },
  {
    serialNo: 12,
    format: '全文',
    type: '格式错误',
    checkDesc: '「L」不应出现Latex代码中未写入内容导致显示???的情况',
    problemDescription: '出现意义不明的段落，请检查是否输入完整',
  },
  {
    serialNo: 13,
    format: '全文',
    type: '格式错误',
    checkDesc: '论文中所有括号应闭合',
    problemDescription: '双括号缺失一半',
  },
  {
    serialNo: 14,
    format: '全文',
    type: '格式错误',
    checkDesc: '论文中文本内容不应超出边界',
    problemDescription: '正文中文本内容超出边界',
  },
  {
    serialNo: 15,
    format: '全文',
    type: '格式错误',
    checkDesc: '英文符号后面应有1个空格',
    problemDescription: '英文符号后缺少空格',
  },
  {
    serialNo: 16,
    format: '全文',
    type: '格式错误',
    checkDesc: '一级标题应居中',
    problemDescription: '一级标题未居中',
  },
  {
    serialNo: 17,
    format: '全文',
    type: '格式错误',
    checkDesc: '中英文标点符号的使用应正确且结合语境',
    problemDescription: '中英文标点符号使用错误，请结合中英文语境检查',
  },
  {
    serialNo: 18,
    format: '全文',
    type: '优化建议',
    checkDesc: '论文中的编号应使用数字序号，不建议使用·',
    problemDescription: '序号使用不合理，建议使用数字序号',
  },
  {
    serialNo: 19,
    format: '全文',
    type: '格式错误',
    checkDesc: '论文中不应出现因编译错误产生的各种不明符号',
    problemDescription: '出现意义不明的符号，请检查是否编译错误',
  },
  {
    serialNo: 20,
    format: '全文',
    type: '优化建议',
    checkDesc: '自然段不应过长，建议控制在一页内',
    problemDescription: '自然段过长，建议精简至一页内',
  },
  {
    serialNo: 21,
    format: '全文',
    type: '语义偏差',
    checkDesc: '英文简称在论文中首次出现时应写全称',
    problemDescription: '首次出现的英文简称未标注英文全称',
  },
  {
    serialNo: 22,
    format: '全文',
    type: '语义偏差',
    checkDesc: '论文中不应出现错别字',
    problemDescription: '存在错别字现象',
  },
  {
    serialNo: 23,
    format: '全文',
    type: '语义偏差',
    checkDesc: '论文中不应出现拼写错误',
    problemDescription: '英文单词拼写错误',
  },
  {
    serialNo: 24,
    format: '全文',
    type: '语义偏差',
    checkDesc: '英文全称的首字母应大写',
    problemDescription: '括号内专有英文名词，应注意首字母大写',
  },
  {
    serialNo: 25,
    format: '全文',
    type: '优化建议',
    checkDesc: '论文中括号内的注释文字不应过多',
    problemDescription: '使用括号注释过于频繁或括号内的内容过多，建议取消括号进行描述',
  },
  {
    serialNo: 26,
    format: '全文',
    type: '语义偏差',
    checkDesc: '单句不应过长，理解较吃力',
    problemDescription: '一句话描述过长，理解较为吃力，建议分句分句叙述',
  },
  {
    serialNo: 27,
    format: '全文',
    type: '语义偏差',
    checkDesc: '论文中阿拉伯数字和汉字数字的使用不应混淆',
    problemDescription: '阿拉伯数字和汉字数字使用混淆，请结合语境检查',
  },
  {
    serialNo: 28,
    format: '全文',
    type: '语义偏差',
    checkDesc: '论文内容不应重复冗余',
    problemDescription: '内容重复冗余',
  },
  {
    serialNo: 29,
    format: '全文',
    type: '语义偏差',
    checkDesc: '论文中不应使用口语化表达，如“我们”、“不太大”等非学术用语',
    problemDescription: '论文中出现口语化表达',
  },
  {
    serialNo: 30,
    format: '全文',
    type: '语义偏差',
    checkDesc: '单句不应重复用词或表述不当',
    problemDescription: '单句中重复用词，或表述不当，请检查',
  },
  {
    serialNo: 31,
    format: '封面',
    type: '格式错误',
    checkDesc: '论文封面应包含：论文题目、研究方向、作者姓名、指导教师姓名、所在学院、专业、提交日期',
    problemDescription: '论文封面内容缺失',
  },
  {
    serialNo: 32,
    format: '封面',
    type: '格式错误',
    checkDesc: '培养单位应填写全称，如“中国科学院软件研究所”',
    problemDescription: '培养单位不应填写简称',
  },
  {
    serialNo: 33,
    format: '封面',
    type: '格式错误',
    checkDesc: '封面上的所有信息应准确无误，与上传论文时输入信息一致',
    problemDescription: '封面信息与上传论文时输入的信息不一致',
  },
  {
    serialNo: 34,
    format: '摘要',
    type: '优化建议',
    checkDesc: '中文摘要应在300～1500字',
    problemDescription: '摘要部分字数过多或过少，建议控制在300~1500字内',
  },
  {
    serialNo: 35,
    format: '摘要',
    type: '格式错误',
    checkDesc: '关键词应在摘要中出现',
    problemDescription: '关键词未在摘要中出现',
  },
  {
    serialNo: 36,
    format: '摘要',
    type: '优化建议',
    checkDesc: '关键词数量应在3～5个',
    problemDescription: '关键词数量不符合要求，建议3～5个',
  },
  {
    serialNo: 37,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '摘要中应有背景介绍',
    problemDescription: '摘要中缺少背景介绍部分',
  },
  {
    serialNo: 38,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '摘要中应列出存在的问题和挑战',
    problemDescription: '摘要中缺少问题和挑战部分',
  },
  {
    serialNo: 39,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '摘要中应介绍主要工作',
    problemDescription: '摘要中未介绍主要工作',
  },
  {
    serialNo: 40,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '中英文摘要的含义应对应一致',
    problemDescription: '中英文摘要的含义不一致，存在未对应部分',
  },
  {
    serialNo: 41,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '摘要中的背景介绍部分篇幅不应过长',
    problemDescription: '摘要中的背景介绍部分篇幅过长，建议精简',
  },
  {
    serialNo: 42,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '摘要中的存在问题和挑战部分篇幅不应过长',
    problemDescription: '摘要中的存在问题和挑战部分篇幅过长，建议精简',
  },
  {
    serialNo: 43,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '摘要中的主要研究内容部分篇幅不应过长',
    problemDescription: '摘要中的主要研究内容部分篇幅过长，建议精简',
  },
  {
    serialNo: 44,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '摘要中的中英文关键词应对应统一',
    problemDescription: '摘要中的中英文关键词未一一对应',
  },
  {
    serialNo: 45,
    format: '摘要',
    type: '语义偏差',
    checkDesc: '英文摘要中不应存在翻译性错误',
    problemDescription: '英文摘要存在翻译性错误',
  },
  {
    serialNo: 46,
    format: '摘要',
    type: '优化建议',
    checkDesc: '摘要中不建议采用编号分段的形式',
    problemDescription: '摘要中采用了分段编号',
  },
  {
    serialNo: 47,
    format: '正文',
    type: '格式错误',
    checkDesc: '公式应使用专用公式输入，而不是英文直接输入',
    problemDescription: '论文公式未使用公式格式，应使用公式插件书写',
  },
  {
    serialNo: 48,
    format: '正文',
    type: '格式错误',
    checkDesc: '正文内引用其他章节内容时，应按照格式书写，如：（3.5节）',
    problemDescription: '引用论文内其他内容时书写不规范',
  },
  {
    serialNo: 49,
    format: '正文',
    type: '优化建议',
    checkDesc: '同一层级下，分段序号的格式应保持一致',
    problemDescription: '同一层级下编号规则使用不一致',
  },
  {
    serialNo: 50,
    format: '正文',
    type: '优化建议',
    checkDesc: '章节标题不应过长，不应在目录中超出一行',
    problemDescription: '单元标题的名称章节标题过长，建议精简',
  },
  {
    serialNo: 51,
    format: '正文',
    type: '语义偏差',
    checkDesc: '国内外研究现状不应仅是罗列事实，需要有自己的评价',
    problemDescription: '国内外研究现状罗列了诸多事实，但评价内容较少，建议增加个人评价',
  },
  {
    serialNo: 52,
    format: '正文',
    type: '格式错误',
    checkDesc: '正文第一章标题应为“绪论”',
    problemDescription: '绪论部分标题错误',
  },
  {
    serialNo: 53,
    format: '目录',
    type: '格式错误',
    checkDesc: '「W」目录内容格式应符合模板要求且包含超链接',
    problemDescription: '目录内容格式错误或未包含超链接',
  },
  {
    serialNo: 54,
    format: '图片',
    type: '格式错误',
    checkDesc: '图片序号应在正文中被引用',
    problemDescription: '图片序号未在正文中引用',
  },
  {
    serialNo: 55,
    format: '图片',
    type: '语义偏差',
    checkDesc: '图片的中英文标题应保持一致',
    problemDescription: '图片标题的中英文表述不一致',
  },
  {
    serialNo: 56,
    format: '图片',
    type: '优化建议',
    checkDesc: '图片题注不应过长，不应在目录中超出一行',
    problemDescription: '图片的标题名称过长，建议缩减',
  },
  {
    serialNo: 57,
    format: '图片',
    type: '格式错误',
    checkDesc: '图片应该有中文题注',
    problemDescription: '图片缺少中文题注',
  },
  {
    serialNo: 58,
    format: '图片',
    type: '格式错误',
    checkDesc: '图片应该有英文题注',
    problemDescription: '图片缺少英文题注',
  },
  {
    serialNo: 59,
    format: '图片',
    type: '格式错误',
    checkDesc: '图片题注应居中',
    problemDescription: '图片题注未居中',
  },
  {
    serialNo: 60,
    format: '图片',
    type: '格式错误',
    checkDesc: '图片题注应位于图片下方',
    problemDescription: '图片题注位置错误，应位于图片下方',
  },
  {
    serialNo: 61,
    format: '图片',
    type: '格式错误',
    checkDesc: '检测图片内容应在边界内',
    problemDescription: '图片内容超出边界',
  },
  {
    serialNo: 62,
    format: '图片',
    type: '格式错误',
    checkDesc: '图片应该居中',
    problemDescription: '图片对齐方式没有居中',
  },
  {
    serialNo: 63,
    format: '图片',
    type: '优化建议',
    checkDesc: '图片来源不应为截图',
    problemDescription: '该图片被系统判定为截图图片，请仔细确认图片来源',
  },
  {
    serialNo: 64,
    format: '图片',
    type: '格式错误',
    checkDesc: '「W」图片与题注间不应存在空行或跨页',
    problemDescription: '图片与题注间存在空行或跨页，请检查',
  },
  {
    serialNo: 65,
    format: '表格',
    type: '格式错误',
    checkDesc: '表格序号应在正文中被引用',
    problemDescription: '表格序号未在正文中引用',
  },
  {
    serialNo: 66,
    format: '表格',
    type: '语义偏差',
    checkDesc: '表格标题的中英文表述应一致',
    problemDescription: '表格标题的中英文表述不一致',
  },
  {
    serialNo: 67,
    format: '表格',
    type: '优化建议',
    checkDesc: '表格题注不应过长，不应在目录中超出一行',
    problemDescription: '表格的标题名称过长，建议缩减',
  },
  {
    serialNo: 68,
    format: '表格',
    type: '格式错误',
    checkDesc: '表格应该有中文题注',
    problemDescription: '表格缺少中文题注',
  },
  {
    serialNo: 69,
    format: '表格',
    type: '格式错误',
    checkDesc: '表格应该有英文题注',
    problemDescription: '表格缺少英文题注',
  },
  {
    serialNo: 70,
    format: '表格',
    type: '格式错误',
    checkDesc: '表格题注应位于表格上方',
    problemDescription: '表格题注位置错误，应位于表格上方',
  },
  {
    serialNo: 71,
    format: '表格',
    type: '格式错误',
    checkDesc: '表格题注应居中',
    problemDescription: '表格题注未居中',
  },
  {
    serialNo: 72,
    format: '表格',
    type: '格式错误',
    checkDesc: '表格内容应在边界内',
    problemDescription: '表格内容超出边界',
  },
  {
    serialNo: 73,
    format: '表格',
    type: '格式错误',
    checkDesc: '表格应该居中',
    problemDescription: '表格对齐方式没有居中',
  },
  {
    serialNo: 74,
    format: '表格',
    type: '格式错误',
    checkDesc: '表格应与表格题注位于同一页',
    problemDescription: '表格与表格题注未在同一页',
  },
  {
    serialNo: 75,
    format: '公式',
    type: '格式错误',
    checkDesc: '公式应标明序号',
    problemDescription: '公式缺少序号',
  },
  {
    serialNo: 76,
    format: '公式',
    type: '格式错误',
    checkDesc: '公式序号应格式正确',
    problemDescription: '公式序号格式错误',
  },
  {
    serialNo: 77,
    format: '参考文献',
    type: '优化建议',
    checkDesc: '参考文献应达到50篇',
    problemDescription: '参考文献数量不足50篇',
  },
  {
    serialNo: 78,
    format: '参考文献',
    type: '优化建议',
    checkDesc: '参考文献应尽量引用近三年内发表的，并处于前沿研究的文献',
    problemDescription: '近三年内研究成果引用数量不足5篇，建议增加',
  },
  {
    serialNo: 79,
    format: '参考文献',
    type: '优化建议',
    checkDesc: '参考文献应尽量使用学术性网站',
    problemDescription: '请检查网站是否属于学术性网站',
  },
  {
    serialNo: 80,
    format: '参考文献',
    type: '优化建议',
    checkDesc: '参考文献中应至少引用一篇中文文献',
    problemDescription: '中文文献引用数量不足1篇',
  },
  {
    serialNo: 81,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '「W」参考文献不应重复',
    problemDescription: '参考文献重复列出',
  },
  {
    serialNo: 82,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '「W」参考文献应全部在正文中被引用',
    problemDescription: '参考文献未在正文中引用',
  },
  {
    serialNo: 83,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '参考文献的格式应正确，不应缺项',
    problemDescription: '参考文献缺失部分内容，如页码',
  },
  {
    serialNo: 84,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '「W」应使用顺序编码制，即参考文献目录中的顺序应按照在正文中引用的顺序展示',
    problemDescription: '参考文献未按照正文中的引用顺序排列',
  },
  {
    serialNo: 85,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '「L」正文引用的参考文献不应空置，导致文章中出现[???]',
    problemDescription: '正文中引用的参考文献缺失，导致文章中出现不明符号',
  },
  {
    serialNo: 86,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '参考文献中论文题目的首字母大小写方式应统一',
    problemDescription: '参考文献中论文题目的首字母大小写未统一，建议统一使用首字母大写',
  },
  {
    serialNo: 87,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '参考文献中论文题目和作者姓名之间应有1个空格',
    problemDescription: '参考文献中论文题目和作者姓名之间应有1个空格',
  },
  {
    serialNo: 88,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '正文中引用参考文献的角标应在文字内容后引用，不应在标点符号后',
    problemDescription: '参考文献的引用应标注在文字后，符号前',
  },
  {
    serialNo: 89,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '正文中引用参考文献的角标不应出现斜体、加粗等格式',
    problemDescription: '正文中引用参考文献的角标不应出现斜体、加粗等格式',
  },
  {
    serialNo: 90,
    format: '参考文献',
    type: '优化建议',
    checkDesc: '参考文献中的会议或期刊不应在“黑名单列表”中',
    problemDescription: '参考文献引用会议或期刊包含（或曾包含）在“黑名单列表”，建议更换',
  },
  {
    serialNo: 91,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '参考文献在正文中应该以右上角标形式进行引用',
    problemDescription: '文献引用格式错误，未使用角标样式',
  },
  {
    serialNo: 92,
    format: '参考文献',
    type: '格式错误',
    checkDesc: '引用连续两篇以上参考文献时，应使用～连接',
    problemDescription: '引用多篇连续的参考文献格式错误，应使用～连接',
  },
]