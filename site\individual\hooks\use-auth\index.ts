import { localStg, sessionStg } from '@paper-review/utils'
import { ElMessage } from 'element-plus'
import { useSlide } from '../../hooks/use-slide/index'
import { router } from '../../router/index'
import { useUserStore } from '../../store/modules/user'

// eslint-disable-next-line regexp/no-unused-capturing-group
const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]*)*)|("[^"]+"))@((\[\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\])|(([a-z0-9][a-z\-0-9]*\.)+[a-z]{2,}))$/i
const phoneRegex = /^1[3-9]\d{9}$/
const userStore = useUserStore()

const verifyCodeForm = reactive({
  account: '',
  code: '',
  slideVerify: false,
  readed: false,
  autoLogin: false,
})
function cleanVerifyCodeForm() {
  verifyCodeForm.account = ''
  verifyCodeForm.code = ''
  verifyCodeForm.slideVerify = false
  verifyCodeForm.readed = false
  verifyCodeForm.autoLogin = false
}

function trimString(str) {
  let start = 0
  let end = str.length - 1

  while (start <= end && /\s/.test(str[start])) {
    start++
  }
  while (end >= start && /\s/.test(str[end])) {
    end--
  }

  return str.substring(start, end + 1)
}

const verifyCodeRules = reactive({
  account: [
    {
      required: true,
      message: '请输入邮箱或者手机号',
      trigger: 'blur',
    },
    {
      validator: (rule: any, value: any, callback: any) => {
        value = trimString(value)
        verifyCodeForm.account = value
        if (value.includes('@')) {
          const isEmail = emailRegex.test(value)
          if (!isEmail) {
            return callback(new Error('请输入正确的邮箱'))
          }
        }
        else {
          const phoneNumberLikeRegex = /^\d+$/
          const isPhone = phoneRegex.test(value)
          if (!isPhone) {
            if (phoneNumberLikeRegex.test(value)) {
              return callback(new Error('请输入正确的手机号'))
            }
            return callback(new Error('请输入正确的邮箱或手机号'))
          }
          else {
            return callback()
          }
        }
        callback()  
      },
    
      trigger: 'blur',
    },
  ],
  slideVerify: [
    { validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        return callback(new Error('请完成验证'))
      }
      callback()  
    }, message: '请完成验证', trigger: 'change' },
  ],
  code: [
    {
      required: true,
      message: '请输入验证码',
      trigger: 'blur',
    },
  ],
  readed: [
    { validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        return callback(new Error('请完成验证'))
      }
      callback()  
    }, message: '请先阅读并同意服务条款、隐私政策', trigger: 'change' },
  ],
    
})

async function sendCode(type) {
  try {
    const res = await generateCaptcha({
      userAccountCode: verifyCodeForm.account,  
      type,  
    })
    if (res.data.code === 500) {
      ElMessage({
        message: res.data.msg,
        type: 'error',
      })
    }
    else {
      ElMessage({
        message: '验证码已发送',
        type: 'success',
      })
    }
    return res
  }
  catch (e) {
    ElMessage({
      message: '验证码发送失败，请稍后重试',
      type: 'error',
    })
  }
}

export function useVerifyCodeRegister() {
  const formRef = useTemplateRef('formRef') as any
  const slideRef = useTemplateRef('slideRef') as any
  const { slideToSended, cleanSlide } = useSlide()
  async function slideSuccees(cleanup: () => void) {
    try {
      await formRef.value?.validateField('account')
      slideToSended(cleanup)
      const res = await sendCode(0)
      if (res.data.code === 500) {
        cleanSlide()
      }
    }
    catch (e) {
      cleanSlide()
      slideRef.value && slideRef.value.clean()
    }
  }
  async function manualTriggerSendCode(type) {
    try {
      await formRef.value?.validateField('account')
      verifyCodeForm.code = ''
      slideToSended()
      const res = await sendCode(type)
      if (res.data.code === 500) {
        cleanSlide()
      }
    }
    catch (e) {
      cleanSlide()
      slideRef.value && slideRef.value.clean()
    }
  }
  
  const submit = () => {
    formRef.value?.validate(async (valid) => {
      if (valid) {
        try {
          const res = await register({
            userAccountCode: verifyCodeForm.account,
            code: verifyCodeForm.code,
          })
          if (!res || res.data.code === 500) {
            return ElMessage({
              message: res.data.msg || '请稍后重试',
              type: 'error',
            })
          }
          const { username, password, code, uuid, userAccountCode, type, token, userId } = res.data.data
  
          sessionStg.set('token', token)
          localStg.set('userInfo', JSON.stringify({ username, password, code, uuid, userAccountCode, type, userId }))
          
          userStore.setUserInfo({ username, password, code, uuid, userAccountCode, type, userId })
          router.push({
            name: 'review',
          })
          await nextTick()
          cleanSlide()
        }
        catch (e) {
          ElMessage({
            message: '注册失败，请稍后重试',
            type: 'error',
          })
        }
      }
    })
  }
  return { formRef, slideRef, ruleForm: verifyCodeForm, rules: verifyCodeRules, manualTriggerSendCode, cleanVerifyCodeForm, slideSuccees, submit, sendCode }
}

export function useVerifyCodeLogin() {
  const formRef = useTemplateRef('formRef') as any
  const slideRef = useTemplateRef('slideRef') as any
  const { slideToSended, cleanSlide } = useSlide()
  async function slideSuccees(cleanup: () => void) {
    try {
      await formRef.value?.validateField('account')
      slideToSended(cleanup)
      const res = await sendCode(1)
      if (res.data.code === 500) {
        cleanSlide()
      }
    }
    catch (e) {
      cleanSlide()
      slideRef.value && slideRef.value.clean()
    }
  }

  async function manualTriggerSendCode(type) {
    try {
      await formRef.value?.validateField('account')
      slideToSended()
      const res = await sendCode(type)
      if (res.data.code === 500) {
        cleanSlide()
      }
    }
    catch (e) {
      cleanSlide()
      slideRef.value && slideRef.value.clean()
    }
  }
  
  const submit = () => {
    formRef.value?.validate(async (valid) => {
      if (valid) {
        try {
          const res = await loginByCaptcha({
            userAccountCode: verifyCodeForm.account,
            code: verifyCodeForm.code,
            loginFree: verifyCodeForm.autoLogin ? 0 : 1,
          })
          if (res.data.code === 500) {
            return ElMessage({
              message: res.data.msg,
              type: 'error',
            })
          }
          const { username, password, code, uuid, userAccountCode, type, token, userId } = res.data.data
  
          if (!verifyCodeForm.autoLogin) {
            localStg.remove('token')
          }
          const tokenStg = verifyCodeForm.autoLogin ? localStg : sessionStg
          tokenStg.set('token', token)
          localStg.set('userInfo', JSON.stringify({ username, password, code, uuid, userAccountCode, type, userId }))

          userStore.setUserInfo({ username, password, code, uuid, userAccountCode, type, userId })
          router.push({
            name: 'review',
          })
          await nextTick()
          cleanSlide()
        }
        catch (e) {
          ElMessage({
            message: '登录失败，请稍后重试',
            type: 'error',
          })
        }
      }
    })
  }
  return { formRef, slideRef, ruleForm: verifyCodeForm, rules: verifyCodeRules, cleanVerifyCodeForm, manualTriggerSendCode, slideSuccees, submit, sendCode }
}
