<script setup lang="ts">
interface CheckResult {
  status: '通过' | '不通过'
  checker: string
  checkTime: string
  comments: string
}

// 这里可以通过props接收数据
defineProps<{
  checkResult?: CheckResult
}>()
</script>

<template>
  <div class="w-full mt-8 mb-14">
    <!-- 第一行 -->
    <div class="w-full flex flex-row justify-between items-center space-x-8">
      <!-- 审核结果 -->
      <div class="flex items-center gap-9">
        <span class="label">导师审核结果</span>
        <span 
          class="px-3 py-1 rounded text-sm" :class="[
            checkResult?.status === '通过' 
              ? 'text-green-600 bg-green-50' 
              : 'text-red-600 bg-red-50',
          ]"
        >
          {{ checkResult?.status || '通过' }}
        </span>
      </div>

      <!-- 审核人 -->
      <div class="flex items-center gap-9">
        <span class="label">审核人</span>
        <span class="content">{{ checkResult?.checker || '余华' }}</span>
      </div>

      <!-- 审核时间 -->
      <div class="flex items-center gap-9">
        <span class="label">审核时间</span>
        <span class="content">{{ checkResult?.checkTime || '2025-01-011 09:00:45' }}</span>
      </div>
    </div>

    <!-- 第二行 -->
    <div class="mt-7 w-full flex flex-row gap-9">
      <div class="label flex-shrink-0">
        全文页数
      </div>
      <div class="content flex-1">
        {{ checkResult?.comments || '标题、章节层级不规范，如第二章节第2句话不正确。请仔细检查！标题、章节层级不规范，如第二章节第2句话不正确。请仔细检查！标题、章节层级不规范，如第二章节第2句话不正确。请仔细检查！' }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.label {
  font-size: 14px;
  width: 84px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
}
.content {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}
</style>
