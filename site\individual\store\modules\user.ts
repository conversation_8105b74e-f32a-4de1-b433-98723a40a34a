import { localStg, sessionStg } from '@paper-review/utils'
import { defineStore } from 'pinia'
import { router } from '../../router/index'

export function getToken() {
  return localStg.get('token') || sessionStg.get('token') || ''
}

export const useUserStore = defineStore('USER', () => {
  const route = useRoute()
  const localStgUserInfo = JSON.parse(localStg.get('userInfo') || '{}') 
  const userInfo = reactive(localStgUserInfo)
  const userConfirm = ref<number>(0) /** 论文检测模板补充页 需要用户二次确认 */

  const setUserInfo = (data) => {
    if (!data || !Object.keys(data).length) {
      Object.keys(userInfo).forEach((key) => {
        delete userInfo[key]
      })
      return
    }

    Object.keys(data).forEach((key) => {
      userInfo[key] = data[key]
    })
  }

  const clearUserInfo = () => {
    localStg.remove('token')
    sessionStg.remove('token')
    localStg.remove('userInfo')
    setUserInfo(null)
  }

  const setUserConfirm = (flag /** 0:no operation  1:cancel  2:confirm 3:waiting */) => {
    userConfirm.value = flag
    if (flag === 2 && route.name === 'login') {
      /** 针对退出登录跳转登录页这一种情况 */
      clearUserInfo()
    }
 
    setTimeout(() => {
      setUserConfirm(0)
    }, 200)
  }

  const logout = async () => {
    router.push({
      name: 'login',
      query: {
        type: 'login',
      },
    })
    await nextTick()
    if (userConfirm.value !== 0) 
      return
    clearUserInfo()
  }
  
  return { userInfo, setUserInfo, logout, setUserConfirm }
})