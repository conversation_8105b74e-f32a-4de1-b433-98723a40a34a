<script setup lang="ts">
// @ts-ignore
import { useDraggable } from '@vueuse/core'
import { ref, useTemplateRef } from 'vue'

const emits = defineEmits(['success', 'fail'])

const tipMsg = ref('请按住滑块，拖到最右边')
const slideRef = useTemplateRef('slideRef')
const slideContainerRef = useTemplateRef('slideContainerRef')
function cleanup(x, animation = false) {
  tipMsg.value = '请按住滑块，拖到最右边'
  const gradient = 20
  if (animation) {
    animationUpdatePosition()
  }
  else {
    x.value = 0
  }
  function animationUpdatePosition() {
    if (x.value <= gradient) 
      return (x.value = 0)
    x.value -= gradient
    requestAnimationFrame(animationUpdatePosition)
  }
}
 
const { x } = useDraggable(slideRef, {
  initialValue: { x: 0, y: 0 },
  containerElement: slideContainerRef,
   
  onEnd(position, event) {
    const containerWidth = slideContainerRef.value?.clientWidth || 0
    const chunkWith = slideRef.value?.clientWidth || 0
    if (chunkWith + position.x >= containerWidth) {
      tipMsg.value = '验证通过'
      emits('success', () => cleanup(x))
    }
    else {
      cleanup(x, true)
      emits('fail')
    }
  },
})
defineExpose({ clean: () => cleanup(x) })
</script>

<template>
  <div ref="slideContainerRef" class="relative overflow-hidden w-[37.2rem] h-[52px] bg-[#f0f0f0] rounded-[8px] box-border flex items-center">
    <div class="absolute h-full opacity-50 bg-[#6ED86E] left-0 top-0 rounded-[8px]" :style="{ width: `${x + 10}px` }" /> 
    <div ref="slideRef" class="absolute cursor-pointer rounded-[8px] h-[52px] w-[60px] flex items-center justify-center" style="background: linear-gradient(90deg, #6ED86E, #1DB989);touch-action: none;" :style="{ left: `${x}px` }">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#fff" d="M9.575 12L5 7.4L6.4 6l6 6l-6 6L5 16.6zm6.6 0L11.6 7.4L13 6l6 6l-6 6l-1.4-1.4z" /></svg>
    </div>

    <span class="select-none w-full flex justify-center text-[#606266] text-[12px] slide-content">{{ tipMsg }}</span>
  </div>
</template>

<style scoped>
.slide-content {
  background: linear-gradient(to right, #333 0%, #333 40%, #fff 50%, #333 60%, #333 100%);
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(0, #333),
    color-stop(0.4, #333),
    color-stop(0.5, #fff),
    color-stop(0.6, #333),
    color-stop(1, #333)
  );
  animation: slidetounlock 3s infinite;
  background-clip: 'text';
  -webkit-background-clip: text;
  -moz-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-text-fill-color: transparent;
}
@keyframes slidetounlock {
  0% {
    background-position: -120px 0;
  }

  100% {
    background-position: 120px 0;
  }
}
</style>