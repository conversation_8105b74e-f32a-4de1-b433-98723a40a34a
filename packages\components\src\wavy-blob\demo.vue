<script setup lang="ts">
import { onMounted, ref } from 'vue'
import WavyBlob from './index.vue'

// 动态进度示例
const dynamicProgress = ref(0)
const animatedProgress = ref(0)
const isAnimating = ref(false)

// 启动动态进度动画
function startDynamicAnimation() {
  if (isAnimating.value) 
    return

  isAnimating.value = true
  dynamicProgress.value = 0

  const interval = setInterval(() => {
    if (dynamicProgress.value < 100) {
      dynamicProgress.value += 1
    }
    else {
      clearInterval(interval)
      isAnimating.value = false
    }
  }, 50)
}

// 重置动态进度
function resetDynamicProgress() {
  dynamicProgress.value = 0
  isAnimating.value = false
}

// 启动平滑动画
function startSmoothAnimation() {
  animatedProgress.value = animatedProgress.value === 0 ? 85 : 0
}

// 组件挂载后启动一次演示
onMounted(() => {
  setTimeout(() => {
    startDynamicAnimation()
  }, 1000)
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">
        WavyBlob 波浪进度组件演示
      </h1>

      <!-- 基础用法 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          基础用法
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              默认样式 (45%)
            </h3>
            <WavyBlob />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              低进度 (15%)
            </h3>
            <WavyBlob :progress="15" />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              高进度 (85%)
            </h3>
            <WavyBlob :progress="85" />
          </div>
        </div>
      </section>

      <!-- 自定义颜色 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          自定义颜色
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              蓝色渐变
            </h3>
            <WavyBlob :progress="60" color="#62FFB8" />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              绿色渐变
            </h3>
            <WavyBlob
              :progress="75"
              color="linear-gradient(135deg, #10b981 0%, #059669 100%)"
            />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              紫色渐变
            </h3>
            <WavyBlob
              :progress="90"
              color="linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)"
            />
          </div>
        </div>
      </section>

      <!-- 动态进度 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          动态进度
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-700">
              快速递增动画
            </h3>
            <div class="flex gap-2 mb-4">
              <button
                :disabled="isAnimating"
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                @click="startDynamicAnimation"
              >
                {{ isAnimating ? "动画中..." : "开始动画" }}
              </button>
              <button
                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                @click="resetDynamicProgress"
              >
                重置
              </button>
              <span class="px-3 py-2 bg-gray-100 rounded text-gray-700">
                {{ dynamicProgress }}%
              </span>
            </div>
            <WavyBlob
              :progress="dynamicProgress"
              color="linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"
            />
          </div>

          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-700">
              平滑过渡动画
            </h3>
            <div class="flex gap-2 mb-4">
              <button
                class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                @click="startSmoothAnimation"
              >
                切换进度
              </button>
              <span class="px-3 py-2 bg-gray-100 rounded text-gray-700">
                {{ animatedProgress }}%
              </span>
            </div>
            <WavyBlob
              :progress="animatedProgress"
              color="linear-gradient(135deg, #ef4444 0%, #dc2626 100%)"
            />
          </div>
        </div>
      </section>

      <!-- 特殊进度值 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          特殊进度值
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              空状态 (0%)
            </h3>
            <WavyBlob :progress="0" />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              少量 (5%)
            </h3>
            <WavyBlob :progress="5" />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              接近满 (95%)
            </h3>
            <WavyBlob :progress="95" />
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              满状态 (100%)
            </h3>
            <WavyBlob
              :progress="100"
              color="linear-gradient(135deg, #10b981 0%, #059669 100%)"
            />
          </div>
        </div>
      </section>

      <!-- API 文档 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-6">
          API 文档
        </h2>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4">
            Props
          </h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    属性名
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    类型
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    默认值
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    说明
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    progress
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    number
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    45
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    进度值，范围 0-100
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    color
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    string
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    linear-gradient(...)
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    波浪颜色，支持纯色或渐变色
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <h3 class="text-lg font-medium text-gray-700 mb-4 mt-8">
            使用示例
          </h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <pre class="text-sm text-gray-800"><code>&lt;!-- 基础用法 --&gt;
&lt;WavyBlob /&gt;

&lt;!-- 自定义进度 --&gt;
&lt;WavyBlob :progress="75" /&gt;

&lt;!-- 自定义颜色 --&gt;
&lt;WavyBlob
  :progress="60"
  color="linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"
/&gt;

&lt;!-- 纯色 --&gt;
&lt;WavyBlob
  :progress="80"
  color="#10b981"
/&gt;</code></pre>
          </div>

          <h3 class="text-lg font-medium text-gray-700 mb-4 mt-8">
            特性说明
          </h3>
          <ul class="list-disc list-inside space-y-2 text-sm text-gray-600">
            <li>支持 0-100 的进度值，自动计算波浪位置</li>
            <li>内置平滑过渡动画，进度变化时自动应用</li>
            <li>支持自定义颜色，包括纯色和渐变色</li>
            <li>响应式设计，自动适应容器大小</li>
            <li>双层波浪效果，营造更真实的液体感</li>
            <li>进度越高，波浪透明度越高，增强视觉效果</li>
          </ul>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
/* 为动画添加平滑过渡 */
.wavy-blob {
  transition: all 0.3s ease-out;
}
</style>
