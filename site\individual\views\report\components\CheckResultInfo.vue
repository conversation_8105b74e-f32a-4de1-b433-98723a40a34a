<script setup lang="ts">
const reportDetail = inject('reportDetail') as any

// approvalStatus:0.未提交 1.审核中 2.不通过 3.审核通过
const approvalStatusMap = {
  0: '未提交',
  1: '审核中',
  2: '不通过',
  3: '审核通过',
}
</script>

<template>
  <div class="w-full mt-8 mb-14">
    <!-- 第一行 -->
    <div class="w-full flex flex-row justify-between items-center space-x-8">
      <!-- 审核结果 -->
      <div class="flex items-center gap-9">
        <span class="label">导师审核结果</span>
        <span 
          class="px-3 py-1 rounded text-sm" :class="[
            reportDetail.approvalStatus === '3'
              ? 'text-green-600 bg-green-50' 
              : 'text-red-600 bg-red-50',
          ]"
        >
          {{ approvalStatusMap[reportDetail.approvalStatus] || '通过' }}
        </span>
      </div>

      <!-- 审核人 -->
      <div class="flex items-center gap-9">
        <span class="label">审核人</span>
        <span class="content">{{ reportDetail?.teacherName || '--' }}</span>
      </div>

      <!-- 审核时间 -->
      <div class="flex items-center gap-9">
        <span class="label">审核时间</span>
        <span class="content">{{ reportDetail?.submitTime || '--' }}</span>
      </div>
    </div>

    <!-- 第二行 -->
    <div v-if="reportDetail?.reason" class="mt-7 w-full flex flex-row gap-9">
      <div class="label flex-shrink-0">
        审核理由
      </div>
      <div class="content flex-1">
        {{ reportDetail?.reason || '--' }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.label {
  font-size: 14px;
  width: 84px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
}
.content {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}
</style>
