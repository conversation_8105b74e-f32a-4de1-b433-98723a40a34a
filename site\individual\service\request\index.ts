import { BACKEND_ERROR_CODE, createFlatRequest } from '@paper-review/axios'
import { ElMessage } from 'element-plus'
import { getToken } from '../../store/modules/user'

export const request: any = createFlatRequest<any, any>(
  {
    baseURL: '/api',
    // baseURL: 'https://user-preview.papercheck.isrc.ac.cn/',
  },
  {
    async onRequest(config) {
      const Authorization = getToken() || ''
      Object.assign(config.headers, { Authorization })
      return config
    },
    isBackendSuccess(response) {
      return response.status === 200 && response.data?.code === BACKEND_ERROR_CODE
    },
    async onBackendFail(response, instance) {
      return null
    },
    transformBackendResponse(response) {
      return response.data.data
    },
    onError(error) {
      if (error.status === 401) {
        ElMessage.closeAll()
        ElMessage({
          message: '登录过期，请重新登录',
          type: 'error',
        })
        router.push({
          name: 'login',
        })
        return Promise.reject(error)
      }
      return Promise.reject(error)
    },
  },
)
