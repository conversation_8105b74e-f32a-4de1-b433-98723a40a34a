<script setup>
import AppLogo from '../../../layout/header/components/app-logo.tsx'

function goReview() {
  router.push({ name: 'review' })
}
</script>

<template>
  <div class="w-fit h-[65px] items-center flex flex-nowrap absolute top-0 cursor-pointer z-[9]" @click="goReview">
    <AppLogo />
    <span class="text-[20px] font-bold ml-[12px]">论文格式检测系统</span>   
  </div>
  <div class="w-[60%] h-screen relative overflow-hidden box-border base:pt-[180px] s-base:pt-[50px]">
    <div class="w-[75rem] h-[38.8rem] bg-[rgba(55,204,176,0.30)] left-[30.2rem] blur-[5rem] absolute top-[-12%] rounded-[50%]" />
    <div class="w-[41rem] h-[31.8rem] bg-[rgba(154,228,136,0.30)] bottom-[-10%] left-[-15%] blur-[5rem] absolute rounded-[50%]" />

    <div class="w-full h-full relative flex content-start flex-wrap justify-start px-[21%] box-border base:scale-[1] s-base:scale-[0.8]">
      <div class="login-title ">
        论文格式检测系统
      </div>
      <div class="login-tip">
        您的论文格式一键检测专家
      </div>
      <img src="../../../assets/image/paper-amico.png" class="login-img">
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-title {
  width: 483px;
  font-size: 60px;
  font-family:
    PingFang SC,
    PingFang SC-Medium;
  font-weight: Medium;
  text-align: left;
  color: rgba(0, 0, 0, 0.85);
  align-content: center;
}
.login-tip {
  width: 483px;
  height: 2.5rem;
  font-size: 1.5rem;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: Regular;
  text-align: left;
  color: rgba(0, 0, 0, 0.65);
  line-height: 2.5rem;
  margin-top: 1.6rem;
}
.login-img {
  width: 483px;
  height: 426px;
  object-fit: contain;
  margin-top: 3.6rem;
}
</style>