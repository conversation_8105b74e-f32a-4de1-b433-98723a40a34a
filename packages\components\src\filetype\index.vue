<script setup lang="ts">
import WORD from '@individual/assets/svg/word_new.svg'
import ZIPNEW from '@individual/assets/svg/ZIP-2.svg'
import SVG from '../../assets/svg/pdf.svg'

defineProps<{
  type: 'pdf' | 'word' | 'x-zip-compressed'
}>()
const assetsMap = {
  'pdf': SVG,
  'word': WORD,
  'docx': WORD,
  'zip': ZIPNEW,
  'x-zip-compressed': ZIPNEW,
  'vnd.openxmlformats-officedocument.wordprocessingml.document': WORD,
}
</script>

<template>
  <img :src="(assetsMap[type] as any)">
</template>

<style scoped>

</style>