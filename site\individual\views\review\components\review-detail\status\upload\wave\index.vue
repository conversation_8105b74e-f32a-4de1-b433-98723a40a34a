<script setup lang="ts">
import { cn } from '@paper-review/utils'
import { templateRef } from '@vueuse/core'
import { createNoise3D } from 'simplex-noise'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

interface WavyBackgroundProps {
  class?: string
  containerClass?: string
  colors?: string[]
  backgroundFill?: string
  blur?: number
  speed?: 'slow' | 'fast'
  waveOpacity?: number
  progress?: number // 进度值，0-100
  waveHeight?: number // 波浪高度，默认30
  [key: string]: any
}

const props = withDefaults(defineProps<WavyBackgroundProps>(), {
  colors: () => ['#000000', '#c084fc', '#e87999'],
  backgroundFill: 'transparent',
  blur: 0,
  speed: 'fast',
  waveOpacity: 1,
  progress: 0,
  waveHeight: 30,
})

// 添加平滑过渡的进度值
const smoothProgress = ref(props.progress!)
let targetProgress = props.progress!
let progressAnimationId: number

const noise = createNoise3D()

// Declare variables with null
let w: number
let h: number
let nt = 0
let ctx: CanvasRenderingContext2D | null = null
let animationId: number

const canvasRef = templateRef<HTMLCanvasElement | null>('canvasRef')

function getSpeed(): number {
  return props.speed === 'slow' ? 0.001 : 0.004
}

function init() {
  const canvas = canvasRef.value
  if (canvas) {
    ctx = canvas.getContext('2d')
    if (ctx) {
      const parent = canvasRef.value.parentElement
      if (parent) {
        w = ctx.canvas.width = parent.clientWidth
        h = ctx.canvas.height = parent.clientHeight
      }

      ctx.filter = `blur(${props.blur}px)`
      window.onresize = () => {
        if (parent) {
          w = ctx!.canvas.width = parent.clientWidth
          h = ctx!.canvas.height = parent.clientHeight
        }
        ctx!.filter = `blur(${props.blur}px)`
      }
      render()
    }
  }
}

// 平滑过渡进度值的函数
function updateSmoothProgress() {
  const diff = targetProgress - smoothProgress.value
  if (Math.abs(diff) > 0.1) {
    smoothProgress.value += diff * 0.05 // 调整这个值来控制过渡速度
    progressAnimationId = requestAnimationFrame(updateSmoothProgress)
  }
  else {
    smoothProgress.value = targetProgress
  }
}

function drawWave(waveCount: number) {
  nt += getSpeed()

  // 计算波浪的基础高度，根据平滑进度从底部到顶部
  const progressRatio = Math.max(0, Math.min(100, smoothProgress.value)) / 100
  const baseHeight = h - h * progressRatio

  // 波浪数量根据colors数组长度或传入参数确定
  const numWaves = waveCount

  // 绘制多层波浪效果
  for (let i = 0; i < numWaves; i++) {
    ctx!.beginPath()
    ctx!.fillStyle = props.colors[i % props.colors!.length]
    // 根据波浪数量动态调整透明度
    // const alphaStep = numWaves > 1 ? 0.8 / (numWaves - 1) : 0;
    // ctx!.globalAlpha = props.waveOpacity! * (0.8 - i * alphaStep);

    // 创建波浪路径
    ctx!.moveTo(0, h)

    for (let x = 0; x <= w; x += 5) {
      const waveHeight
        = noise(x / 800, 0.3 * i, nt + i * 0.5) * props.waveHeight!
      const y = baseHeight + waveHeight - i * 10
      ctx!.lineTo(x, y)
    }

    // 闭合路径到底部
    ctx!.lineTo(w, h)
    ctx!.lineTo(0, h)
    ctx!.fill()
    ctx!.closePath()
  }

  // 重置透明度
  ctx!.globalAlpha = 1
}

function render() {
  if (ctx) {
    // 清除画布
    ctx.clearRect(0, 0, w, h)

    // 绘制背景
    ctx.fillStyle = props.backgroundFill!
    ctx.fillRect(0, 0, w, h)

    // 绘制波浪
    drawWave(props.colors!.length || 1)

    animationId = requestAnimationFrame(render)
  }
}

// 监听进度变化
watch(
  () => props.progress,
  (newProgress) => {
    targetProgress = newProgress!
    cancelAnimationFrame(progressAnimationId)
    updateSmoothProgress()
  },
  { immediate: true },
)

onBeforeUnmount(() => {
  cancelAnimationFrame(animationId)
  cancelAnimationFrame(progressAnimationId)
})

const isSafari = ref(false)
onMounted(() => {
  isSafari.value
    = typeof window !== 'undefined'
    && navigator.userAgent.includes('Safari')
    && !navigator.userAgent.includes('Chrome')

  init()
})
</script>

<template>
  <div
    :class="
      cn(
        'h-full w-full flex flex-col items-center justify-center',
        props.containerClass,
      )
    "
  >
    <canvas
      id="canvas"
      ref="canvasRef"
      class="absolute z-0"
      :style="{ filter: isSafari ? `blur(${props.blur}px)` : undefined }"
    />
    <div :class="cn('relative z-10', props.class)">
      <slot />
    </div>
  </div>
</template>
