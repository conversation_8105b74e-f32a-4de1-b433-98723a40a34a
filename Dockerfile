# FROM node AS build
# WORKDIR /source
# COPY . /source

# ENV PATH=$PATH:/root/.npm-global/bin
# # 设置 Node.js 的最大内存限制为 8GB
# ENV NODE_OPTIONS="--max-old-space-size=8192"
# #设置缓存目录
# # RUN npm config set cache /npmcache
# # RUN npm config set prefix '~/.npm-global'
# RUN npm config set registry https://repo.huaweicloud.com/repository/npm/
# RUN npm install pnpm@9.11.0 -g 

# RUN rm -rf package-lock.json node_modules

# RUN pnpm i -w && pnpm run build:individual 
# RUN cp -r dist /opt/paper-front

# FROM nginx
# COPY --from=build /opt/paper-front /usr/share/nginx/html
# # COPY nginx.conf /etc/nginx/conf.d/default.conf 
# ENV LC_ALL en_US.UTF-8
# ENV LANG en_US.UTF-8

FROM swr.cn-north-4.myhuaweicloud.com/iscas-isrc/front-base:latest AS build
WORKDIR /source
FROM nginx:alpine
COPY dist /usr/share/nginx/html

ENV LC_ALL en_US.UTF-8
ENV LANG en_US.UTF-8
