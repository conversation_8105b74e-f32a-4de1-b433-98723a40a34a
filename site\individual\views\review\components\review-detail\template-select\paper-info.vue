<script setup lang="ts">
// @ts-ignore
import { FileType } from '@paper-review/components'

const { frontTempFileInfo } = inject<any>('useFrontTempFileInfo')

const fileTypeArr = frontTempFileInfo.value.fileName.split('.')
const fileType = fileTypeArr[fileTypeArr.length - 1] === 'pdf' ? 'pdf' : fileTypeArr[fileTypeArr.length - 1] || 'word'

const paperInfo = reactive({
  author: '',
  school: '',
  // titleCn: frontTempFileInfo.value.name.slice(0, 50),
  titleCn: '',
  titleEN: '',
  degree: '',
})

const maxLen = {
  author: 20,
  school: 20,
  titleCn: 50,
  titleEN: 90,
}

defineExpose({ paperInfo })
const showDegree = ref(false)
const selectRef = useTemplateRef('selectRef')

initPaperInfo()
// coverResult: "{\"degree\": \"硕士毕业论文\", \"titleCn\": null, \"author\": \"朱自然\", \"school\": \"中科院软件所\"}"
function initPaperInfo() {
  Object.keys(frontTempFileInfo.value).forEach((key) => {
    paperInfo[key] = frontTempFileInfo.value[key] || ''
  })
}
function editPaperInfo(e: Event, key: string) {
  const target = e.target as HTMLSpanElement

  // @ts-ignore
  if (paperInfo[key].length >= maxLen[key] && target.textContent?.length >= maxLen[key]) {
    return target.textContent = paperInfo[key] 
  }
  if (target.textContent && target.textContent?.length > maxLen[key]) {
    const existLen = paperInfo[key].length
    paperInfo[key] = paperInfo[key] + target.textContent.slice(0, maxLen[key] - existLen)
    target.textContent = paperInfo[key]
    return
  }
  paperInfo[key] = target.textContent
}
function focus() {
  showDegree.value = true
}
function blurSelect() {
  showDegree.value = false
}
function truncateString(str, maxLength = 40) {
  if (str.length <= maxLength) {
    return str // 如果字符串长度小于等于 maxLength，则直接返回
  }

  if (maxLength < 40) {
    console.warn('maxLength should greater or equal 40')
    return str
  }
  const halfLength = Math.floor(maxLength / 2)
  const start = str.substring(0, halfLength)
  const end = str.substring(str.length - halfLength)

  return `${start}...${end}`
}
</script>

<template>
  <div class="border-b-[1px] border-[#e8e8e8] h-[160px] pb-[30px] flex flex-nowrap items-center w-full relative">
    <FileType :type="fileType" class="h-[100px] mr-[3%]" />  
    <div class="text-[14px] w-full h-full flex flex-wrap relative">
      <div class="w-full text-blod text-[20px] papaer-title mb-[10px] whitespace-nowrap" @input="editPaperInfo($event, 'titleCn')">
        {{ truncateString(frontTempFileInfo.fileName) }}
      </div>
      <div class="w-full mb-[10px] edit-container">
        <span class="text-[rgba(0,0,0,0.45)] mr-[24px]">中文标题</span>
        <span class="text-[rgba(0,0,0,0.85)] w-fits edit-input" contenteditable @input="editPaperInfo($event, 'titleCn')">{{ paperInfo.titleCn }}</span>
      </div>
      <div class="w-full mb-[10px] edit-container flex flex-nowrap items-center">
        <span class="text-[rgba(0,0,0,0.45)] mr-[24px]">英文标题</span>
        <span class="text-[rgba(0,0,0,0.85)] inline-block whitespace-nowrap edit-span edit-input max-w-[80%] overflow-hidden" style="padding-left: 10px;" contenteditable @input="editPaperInfo($event, 'titleEN')">{{ paperInfo.titleEN }}</span>
      </div>
      <div class="w-[82%] flex flex-nowrap flex-1 justify-between">
        <div class="edit-container" style="flex: 1;">
          <span class="text-[rgba(0,0,0,0.45)] mr-[24px]">作者<span class="opacity-0"><!-- 不显示占据两个字符实现对齐 -->作者</span></span>
          <span class="text-[rgba(0,0,0,0.85)] edit-span edit-input" contenteditable @input="editPaperInfo($event, 'author')">{{ paperInfo.author }}</span>
        </div>
        <div class="edit-container" style="flex: 1;">
          <span class="text-[rgba(0,0,0,0.45)] mr-[24px]">学校</span>
          <span class="text-[rgba(0,0,0,0.85)] edit-span edit-input" contenteditable @input="editPaperInfo($event, 'school')">{{ paperInfo.school }}</span>
        </div>
        <div class="text-center w-fit flex edit-container items-center box-border"> 
          <span class="text-[rgba(0,0,0,0.45)] mr-[24px]">学位</span>
          <span class="text-[rgba(0,0,0,0.85)] inline-block degree edit-input text-left content-center box-border pl-[5px]" :class="{ 'background-color': showDegree ? '#eaf9f9' : '' }" @click="focus">
            <span v-show="!showDegree">{{ paperInfo.degree }}</span>
            <el-select v-show="showDegree" ref="selectRef" v-model="paperInfo.degree" size="small" @blur="blurSelect">
              <el-option
                label="学士"
                value="学士"
              />
              <el-option
                label="硕士"
                value="硕士"
              />
              <el-option
                label="博士"
                value="博士"
              />
            </el-select>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
[contenteditable]:focus {
  background: #eaf9f9;
  border-radius: 8px;
  padding-left: 10px;
  padding-right: 10px;
  outline: none;
}
.edit-span {
  display: inline-block;
  min-width: 100px;
}
.degree {
  border-radius: 8px;
  box-sizing: border-box;
  // padding-left: 5px;
  // padding-right: 5px;
  display: inline-block;
  min-width: 110px;
  height: 29px;
}
.edit-input {
  display: inline-block;
  padding: 3px;
}
.edit-container:hover {
  .edit-input {
    border-radius: 8px;
    background-color: #e9e9e9;
    // padding: 3px;
    // padding-left: 10px;
    // padding-right: 10px;
  }
}
</style>