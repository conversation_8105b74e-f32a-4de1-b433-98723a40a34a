<script setup lang="ts">
import DemoWavyBackground from '../index.vue'

const progress = ref(45)
const isAutoPlaying = ref(false)
let autoInterval: number | null = null

// 颜色控制
const colors = ref(['#43E3CC30', '#62FFB840'])
const newColor = ref('#62FFB820')

// 波浪高度控制
const waveHeight = ref(60)

// 添加颜色
function addColor() {
  if (colors.value.length < 5) {
    colors.value.push(newColor.value)
  }
}

// 删除颜色
function removeColor(index: number) {
  if (colors.value.length > 1) {
    colors.value.splice(index, 1)
  }
}

// 更新颜色
function updateColor(index: number, color: string) {
  colors.value[index] = color
}

// 自动演示功能
function startAutoDemo() {
  if (autoInterval) 
    clearInterval(autoInterval)
  isAutoPlaying.value = true
  progress.value = 0

  autoInterval = setInterval(() => {
    if (progress.value >= 100) {
      stopAutoDemo()
    }
    else {
      progress.value += 2
    }
  }, 100)
}

function stopAutoDemo() {
  if (autoInterval) {
    window.clearInterval(autoInterval)
    autoInterval = null
  }
  isAutoPlaying.value = false
}

function resetProgress() {
  stopAutoDemo()
  progress.value = 0
}

onBeforeUnmount(() => {
  if (autoInterval) 
    window.clearInterval(autoInterval)
})
</script>

<template>
  <div class="page-container">
    <!-- 控制面板 -->
    <div class="controls-panel">
      <div class="progress-display">
        <span class="progress-label">进度:</span>
        <span class="progress-value">{{ Math.round(progress) }}%</span>
      </div>

      <div class="slider-container">
        <input
          v-model="progress"
          type="range"
          min="0"
          max="100"
          step="1"
          class="progress-slider"
          :disabled="isAutoPlaying"
        >
      </div>

      <div class="button-group">
        <button
          :disabled="isAutoPlaying"
          class="control-btn demo-btn"
          @click="startAutoDemo"
        >
          {{ isAutoPlaying ? "演示中..." : "自动演示" }}
        </button>

        <button
          :disabled="!isAutoPlaying"
          class="control-btn stop-btn"
          @click="stopAutoDemo"
        >
          停止
        </button>

        <button class="control-btn reset-btn" @click="resetProgress">
          重置
        </button>
      </div>
    </div>

    <!-- 颜色控制 -->
    <div class="color-controls">
      <h3 class="control-title">
        颜色控制
      </h3>
      <div class="color-list">
        <div v-for="(color, index) in colors" :key="index" class="color-item">
          <input
            type="color"
            :value="color.substring(0, 7)"
            class="color-picker"
            @input="
              updateColor(
                index,
                ($event.target as HTMLInputElement).value + color.substring(7),
              )
            "
          >
          <input
            type="text"
            :value="color"
            class="color-input"
            placeholder="#RRGGBBAA"
            @input="
              updateColor(index, ($event.target as HTMLInputElement).value)
            "
          >
          <button
            v-if="colors.length > 1"
            class="remove-color-btn"
            @click="removeColor(index)"
          >
            ×
          </button>
        </div>
      </div>
      <div v-if="colors.length < 5" class="add-color-section">
        <input
          v-model="newColor"
          type="text"
          placeholder="#RRGGBBAA"
          class="color-input"
        >
        <button class="add-color-btn" @click="addColor">
          添加颜色
        </button>
      </div>
    </div>

    <!-- 波浪高度控制 -->
    <div class="wave-height-controls">
      <h3 class="control-title">
        波浪高度
      </h3>
      <div class="height-control">
        <input
          v-model="waveHeight"
          type="range"
          min="10"
          max="200"
          step="5"
          class="height-slider"
        >
        <span class="height-value">{{ waveHeight }}px</span>
      </div>
    </div>

    <!-- 波浪背景 -->
    <div class="w-[630px] h-[320px]">
      <DemoWavyBackground
        :colors="colors"
        :progress="Number(progress)"
        :wave-height="waveHeight"
      >
        {{ progress }}
      </DemoWavyBackground>
    </div>

    <!-- 代码中使用： -->
    <!-- <div class="w-[630px] h-[320px]">
      <DemoWavyBackground
        :colors="['#43E3CC30', '#62FFB840']"
        :progress="45"
        :wave-height="60"
      >
        {{ 45 }}
      </DemoWavyBackground>
    </div> -->
  </div>
</template>

<style scoped>
.page-container {
  position: relative;
}

.controls-panel {
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 280px;
}

.progress-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-label {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.progress-value {
  font-size: 18px;
  font-weight: 700;
  color: #3b82f6;
}

.slider-container {
  margin-bottom: 15px;
}

.progress-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  cursor: pointer;
  transition: opacity 0.2s;
}

.progress-slider:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.progress-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

.progress-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

.button-group {
  display: flex;
  gap: 8px;
}

.control-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.demo-btn {
  background: #3b82f6;
  color: white;
}

.demo-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.demo-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.stop-btn {
  background: #ef4444;
  color: white;
}

.stop-btn:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

.stop-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.reset-btn {
  background: #6b7280;
  color: white;
}

.reset-btn:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.color-controls,
.wave-height-controls {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.control-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.color-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-picker {
  width: 40px;
  height: 30px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.color-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  font-family: monospace;
}

.remove-color-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: #ef4444;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
}

.remove-color-btn:hover {
  background: #dc2626;
}

.add-color-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

.add-color-btn {
  padding: 6px 12px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
}

.add-color-btn:hover {
  background: #059669;
}

.height-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.height-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  cursor: pointer;
}

.height-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.3);
}

.height-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.3);
}

.height-value {
  font-weight: 600;
  color: #10b981;
  min-width: 50px;
  text-align: right;
}
</style>
