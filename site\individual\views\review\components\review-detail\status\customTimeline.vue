<script setup lang="ts">
const props = defineProps<{
  loading: boolean
  uploaded: boolean
  dragOver: boolean
  uploadCheckInfo: any
  typeName: string
}>()
const zipErrorMessageRecord = ref('')
watch(() => props.uploadCheckInfo, (val) => {
  console.log('this is val:', val)
  const { zipStatus, zipErrorMessage, taskId } = val
  zipErrorMessageRecord.value = zipErrorMessage
})

const { setStatus } = inject('useStatus') as any
const STATUS = inject('STATUS') as any

const status = computed(() => {
  if (props.loading) {
    if (props.uploaded) 
      return 2
    else 
      return 1
  }
  else if (!props.loading) {
    if (props.uploaded) 
      return 2
  }
  else if (props.uploadCheckInfo && props.uploadCheckInfo.taskId) {
    return props.uploadCheckInfo.zipStatus
  }
  return 0
})
const showFlag = computed(() => {
  if ((props.uploadCheckInfo && props.uploadCheckInfo.taskId) || status.value > 0) {
    return 2
  }
  return 1
})
</script>

<template>
  <div class="relative">
    <div class="flex flex-nowrap items-start text-[rgba(0,0,0,0.85)] text-[14px]">
      <div class="mr-[14px] relative flex justify-center flex-wrap w-[20px]">
        <div v-if="status <= 1" class="h-[20px] w-[20px] bgline">
          <img src="@individual/assets/svg/loading_new.svg" class="left-[-5px] top-[-5px] w-[20px] h-[20px] animate-spin">
        </div>
        <img v-else-if="status > 1" src="@individual/assets/svg/success_icon.svg" class="left-[-5px] top-[-5px] w-[20px] h-[20px]">
        <div class="w-[2px] h-[32px]  flex bf-[red] top-[20px] border-l-[2px] border-l-solid border-l-[#e4e7ed]" />
      </div>
      <span>
        将{{ typeName }}文件安全传输至服务器
      </span>
    </div>
    <div class="flex flex-nowrap items-start text-[rgba(0,0,0,0.85)] text-[14px]">
      <div class="mr-[14px] relative flex justify-center flex-wrap w-[20px]">
        <img v-if="parseInt(zipErrorMessageRecord) === 1" src="@individual/assets/svg/Close-Circle-Fill.svg" class="left-[-5px] top-[-5px] w-[20px] h-[20px]">

        <div v-else-if="status <= 2 && status > 1" class="h-[20px] w-[20px] bgline">
          <img src="@individual/assets/svg/loading_new.svg" class="left-[-5px] top-[-5px] w-[20px] h-[20px] animate-spin">
        </div>

        <img v-else-if="status > 2" src="@individual/assets/svg/success_icon.svg" class="left-[-5px] top-[-5px] w-[20px] h-[20px]">
        <div v-else class="h-[20px] w-[20px] border-[1px] border-solid border-[#e9e9e9] rounded-[50%] bg-[#fff] left-[-5px] top-[-5px]" />
        <div class="w-[2px] h-[32px]  flex bf-[red] top-[20px] border-l-[2px] border-l-solid border-l-[#e4e7ed]" />
      </div>
      <span>
        全力扫描并解析文件
      </span>
    </div>
    <div class="flex flex-nowrap items-start text-[rgba(0,0,0,0.85)] text-[14px]">
      <div class="mr-[14px] relative flex justify-center flex-wrap w-[20px]">
        <div v-if="status <= 3 && status > 2" class="h-[20px] w-[20px] bgline">
          <img src="@individual/assets/svg/loading_new.svg" class="left-[-5px] top-[-5px] w-[20px] h-[20px] animate-spin">
        </div>
        <img v-else-if="status > 3" src="@individual/assets/svg/success_icon.svg" class="left-[-5px] top-[-5px] w-[20px] h-[20px]">
        <div v-else class="h-[20px] w-[20px] border-[1px] border-solid border-[#e9e9e9] rounded-[50%] bg-[#fff] left-[-5px] top-[-5px]" />
        <!-- <div class="w-[2px] h-[32px]  flex bf-[red] top-[20px] border-l-[2px] border-l-solid border-l-[#e4e7ed]" /> -->
      </div>
      <span>
        提取文件关键信息
      </span>
    </div>
  </div>
</template>

<style scoped>

</style>