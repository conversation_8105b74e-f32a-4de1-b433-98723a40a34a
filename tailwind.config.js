/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin')

const scrollPlugin = plugin(({ addComponents }) => {
  addComponents({
    '.scrollbar-thin': {
      '&::-webkit-scrollbar': {
        width: '3px',
      },
      '&::-webkit-scrollbar-thumb': {
        'width': '3px',
        'border-radius': '8px',
        ' background-color': '#e8e8e8',
      },
    },
  })
})

export default {
  content: [
    './index.html',
    './src/*.{vue,js,ts,jsx,tsx}',
    './site/**/*.{vue,js,ts,jsx,tsx}',
    './packages/**/*.{vue,js,ts,jsx,tsx}',
  ],

  theme: {
    extend: {
      screens: {
        'base': '1441px',
        's-base': '641px',
      },
      colors: {
        'active': '#06b190',
        'bg-active': '#EAF9F9',
        'linear-gradient': 'linear-gradient(90deg, #6ED86E, #1DB989)',
      },
    },
  },
  plugins: [scrollPlugin],
}
