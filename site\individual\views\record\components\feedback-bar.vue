<script setup lang="ts">
import { Close, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { submitFeedBack } from '../../../service/api/sys'
import Editor from './editor.vue'

const props = defineProps<{
  previewRow: any
}>()
const editorRef = useTemplateRef<any>('editorRef')
const feedbackSubmitLoading = ref(false)

const feedBackDialogVisible = ref(false)
function openFeedBackDialog() {
  feedBackDialogVisible.value = true
}
function closeFeedBackDialog() {
  feedBackDialogVisible.value = false
}

const tempFeedbackData = ref('')
function getHtml(data) {
  tempFeedbackData.value = data
}

async function submit() {
  feedbackSubmitLoading.value = true
  const params = {
    paperId: props.previewRow.id,
    feedback: tempFeedbackData.value,
  }
  await submitFeedBack(params)
  feedbackSubmitLoading.value = false
  feedBackDialogVisible.value = false
  ElMessage.success('提交反馈成功，相关人员会尽快为您处理，请耐心等候。')
  editorRef.value.clear()
}
function cancel() {
  feedBackDialogVisible.value = false
  // editorRef.value.clear()
}
function clear() {
  editorRef.value.clear()
}

defineExpose({
  clear,
  openFeedBackDialog,
})
</script>

<template>
  <div class="bg-[#EAF9F9] rounded-md border-[#06B190] border-[1px] ">
    <el-alert
      :closable="false"
      class="!bg-transparent !border-none"
    >
      <div class="flex items-center align-middle">     
        <el-icon style="margin-right: 10px;">
          <InfoFilled style="color: #06b190;width: 14px;height: 14px;" />
        </el-icon>
        <span class="inline-block font-[12px]" style="color: #000;">若您在使用过程中有任何疑问或建议，欢迎反馈，您的宝贵意见将帮助我们不断优化产品，减少您后续检测错误量。</span>
        <el-link type="primary" class="ml-2 text-teal-500" @click="openFeedBackDialog">
          提交反馈
        </el-link>
      </div>
    </el-alert>
    <el-dialog v-model="feedBackDialogVisible" v-loading="feedbackSubmitLoading" :modal="false" :show-close="false" style="width: 550px;">
      <div class="w-full flex justify-between items-center">
        <span class="inline-block font-bold text-[15px]">提交反馈</span>
        <el-icon style="width: 20px;height: 20px;cursor: pointer;">
          <Close style="font-size: 15px;width: 20px;height: 20px;" @click="closeFeedBackDialog" />
        </el-icon>
      </div>
      <div class="mt-6">
        <Editor ref="editorRef" @get-html="getHtml" />
      </div>
      <div class="w-full flex justify-center mt-6 bg-linear-gradient">
        <el-button style="background-image: linear-gradient(90deg, #6ED86E, #1DB989);color:#fff" @click="submit">
          确认
        </el-button>
        <el-button @click="cancel">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>

</style>