import antfu from "@antfu/eslint-config";
import eslintHandleProgressPlugin from "eslint-plugin-file-progress";

export default antfu({
  formatters: {
    css: true,
  },
  plugins: { eslintHandleProgressPlugin },
  rules: {
    "style/eol-last": "off",
    "style/no-trailing-spaces": "off",
    "no-restricted-globals": "off",
    "node/prefer-global/buffer": "off",
    "eslintHandleProgressPlugin/activate": 1,
    "ts/ban-ts-comment": "off",
    "unused-imports/no-unused-vars": "off",
    "style/multiline-ternary": "off",
    "no-console": "warn",
    "vue/operator-linebreak": "off",
  },
  ignores: ["dist", "node_modules", "commitlint.config.js", "tsconfig.*.json"],
  settings: {
    progress: {
      hide: false, // use this to hide the progress message, can be useful in CI
      hideFileName: false, // use this to hide the file name, would simply show "Linting..."
      successMessage: "<PERSON>t Successfully",
    },
  },
});
