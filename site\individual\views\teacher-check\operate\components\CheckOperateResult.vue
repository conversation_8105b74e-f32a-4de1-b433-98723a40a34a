<script setup lang="ts">
withDefaults(defineProps<{
  status: boolean 
  person: string
  time: string
  reviewReason: string
}>(), {
  status: true,
  person: '张三',
  time: '2024-01-01 12:00:00',
  reviewReason: '标题、章节层级不规范，如第二章节第2句话不正确。请仔细检查！标题、章节层级不规范，如第二章节第2句话不正确。请仔细检查！标题、章节层级不规范，如第二章节第2句话不正确。',
})
</script>

<template>
  <div class="w-full">
    <div 
      aria-label="图标" 
      class="mt-[40px] w-full flex flex-col justify-center items-center text-center"
    >
      <img 
        v-if="status"
        class="w-[108px] h-[108px]"
        src="@individual/assets/image/icon_审核通过.webp"
      >
      <img 
        v-else
        class="w-[108px] h-[108px]"
        src="@individual/assets/image/icon_审核不通过.webp"
      >

      <span class="text-[24px] font-medium my-4">
        {{ status ? '审核通过' : '审核不通过' }}
      </span>
    </div>

    <div class="max-w-[400px] mx-auto text-[12px]">
      <div 
        aria-label="审核的信息"
        class="
          flex flex-row justify-between items-center flex-wrap
        "
      >
        <div> 
          <span class="opacity-65">审核人：</span>
          <span class="opacity-45">{{ person }}</span>
        </div>

        <div>
          <span class="opacity-65">审核时间：</span>
          <span class="opacity-45">{{ time }}</span>
        </div>
      </div>

      <div v-if="reviewReason" aria-label="审核理由" class="mt-4">
        <span class="opacity-65">审核理由：</span>
        <span class="opacity-45">{{ reviewReason }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
