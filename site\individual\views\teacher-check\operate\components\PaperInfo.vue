<script setup lang="ts">
withDefaults(defineProps<{
  wordCount?: string | number
  formatError?: string | number
  semanticDeviation?: string | number
  improvementSuggestions?: string | number
}>(), {
  wordCount: '0',
  formatError: '0',
  semanticDeviation: '0',
  improvementSuggestions: '0',
})
</script>

<template>
  <div 
    class="
      grid 
      sm:grid-cols-1
      md:grid-cols-2
      lg:grid-cols-4
      gap-4
    "
  >
    <div class="bg-blue-50 rounded-lg p-4 flex items-center gap-3">
      <div class="rounded-full p-2">
        <img src="@individual/assets/svg/字数统计.svg" class="w-12 h-12">
      </div>
      <div>
        <div class="text-sm text-gray-600">
          字数统计
        </div>
        <div class="text-xl font-semibold text-gray-800">
          {{ wordCount }}<span class="text-sm ml-1">字</span>
        </div>
      </div>
    </div>
          
    <div class="bg-red-50 rounded-lg p-4 flex items-center gap-3">
      <div class="rounded-full p-2">
        <img src="@individual/assets/svg/格式错误.svg" class="w-12 h-12">
      </div>
      <div>
        <div class="text-sm text-gray-600">
          格式错误
        </div>
        <div class="text-xl font-semibold text-gray-800">
          {{ formatError }}<span class="text-sm ml-1">项</span>
        </div>
      </div>
    </div>
          
    <div class="bg-green-50 rounded-lg p-4 flex items-center gap-3">
      <div class="rounded-full  p-2">
        <img src="@individual/assets/svg/语义偏差.svg" class="w-12 h-12">
      </div>
      <div>
        <div class="text-sm text-gray-600">
          语义偏差
        </div>
        <div class="text-xl font-semibold text-gray-800">
          {{ semanticDeviation }}<span class="text-sm ml-1">项</span>
        </div>
      </div>
    </div>
          
    <div class="bg-purple-50 rounded-lg p-4 flex items-center gap-3">
      <div class="rounded-full  p-2">
        <img src="@individual/assets/svg/优化建议.svg" class="w-12 h-12">
      </div>
      <div>
        <div class="text-sm text-gray-600">
          优化建议
        </div>
        <div class="text-xl font-semibold text-gray-800">
          {{ improvementSuggestions }}<span class="text-sm ml-1">项</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
