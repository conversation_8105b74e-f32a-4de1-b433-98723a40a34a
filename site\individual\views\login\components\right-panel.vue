<script setup lang="ts">
import Login from './login/login.vue'
import Register from './register/register.vue'

const route = useRoute()
const router = useRouter()

enum PANNEL {
  LOGIN = 'login',
  REGISTER = 'register',
}

const pannel = ref((route.query.type && PANNEL[(route.query.type as string).toUpperCase()]) || PANNEL.REGISTER)

function switchPannel(p: PANNEL) {
  pannel.value = p
  router.replace({ name: 'login', query: { type: p } })
}
provide('switchPannel', { PANNEL, switchPannel })

const userInfo = localStg.get('userInfo')

function initStatePannel() {
  if (route.query.type) 
    return
  if (!userInfo) 
    return switchPannel(PANNEL.REGISTER)
  switchPannel(PANNEL.LOGIN)
}
initStatePannel()
</script>

<template>
  <Login v-if="pannel === PANNEL.LOGIN" />
  <Register v-if="pannel === PANNEL.REGISTER" />
</template>

<style lang="scss" scoped>
.tab-item {
  cursor: pointer;
  box-sizing: border-box;
  font-size: 24px;
  line-height: 20px;
  font-family:
    PingFang SC,
    PingFang SC-Medium;
  font-weight: Medium;
  text-align: left;
  color: rgba(0, 0, 0, 0.85);
}
.tab-item-active {
  cursor: pointer;
  box-sizing: border-box;
  font-size: 24px;
  line-height: 20px;
  position: relative;
  color: #06b190;
  &::after {
    position: absolute;

    height: 2px;
    box-sizing: border-box;
    width: 100%;
    content: '';
    display: inline-block;
    background-color: #06b190;
    top: 160%;
    left: 0;
  }
}
:deep(.el-input__wrapper) {
  border-radius: 8px;
}
</style>
