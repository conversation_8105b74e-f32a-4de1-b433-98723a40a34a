<script setup lang="ts">
import { useIntervalFn } from '@vueuse/core'
import { ElMessage } from 'element-plus'
import { getManualCheckPaperList, updateTemplatePaperReviewRecord } from '../../service/api/sys'
import { checkList } from './check-list'

const tableData = ref([])
const total = ref(0)
const pageInfo = ref({
  current: 1,
  size: 10,
})

function getList() {
  getManualCheckPaperList({
    pageNum: pageInfo.value.current,
    pageSize: pageInfo.value.size,
  }).then((res) => {
    tableData.value = res.data.data.list
    total.value = res.data.data.total
  })
}

function handleCurrentChange(val: number) {
  pageInfo.value.current = val
  getList()
}

function handleSizeChange(val: number) {
  pageInfo.value.size = val
  pageInfo.value.current = 1
  getList()
}
getList()

const editRow = ref<any>(null)
const dialogTableVisible = ref(false)
const fillList = ref<any>([])
function openDialog(item) {
  const result = (item.reviewResultTemplate && item.reviewResultTemplate.resultList) || []
  fillList.value = checkList.map((item: any, index) => {
    return {
      ...item,
      originalText: result[index]?.originalText,
      pageNum: result[index]?.pageNum,
    }
  })
  editRow.value = item
  dialogTableVisible.value = true
  setIntervalSave()
}

let intervalPause: any = null
function setIntervalSave() {
  const { pause } = useIntervalFn(() => {
    save(false)
    intervalPause = pause
  }, 1000 * 1)
}

async function save(closeDialogFlag: boolean = true) {
  try {
    await updateTemplatePaperReviewRecord({
      id: editRow.value.id,
      reviewResultTemplate: { resultList: fillList.value },
    })

    if (closeDialogFlag) {
      getList()
      closeDialog()
    }
  }
  catch (e) {
    ElMessage({
      type: 'error',
      message: '保存失败，请重试保存。',
    })
  }
}

function closeDialog() {
  intervalPause()
  editRow.value = null
  fillList.value = []
  dialogTableVisible.value = false
}
function cancel() {
  closeDialog()
}
async function download(scope, e) {
  e.stopPropagation()
  // 新窗口下载文件
  window.open(scope.row.wordUrl, '_blank')
}
</script>

<template>
  <div class="w-[100vw] h-[100vh] min-h-[600px] min-w-[1440px] pt-[10%] flex flex-wrap justify-center bg-container content-center">
    <div class="w-full mx-[10%] bg-[#fff] p-[20px] rounded-[8px]">
      <el-table :data="tableData" style="width: 100%;">
        <el-table-column prop="taskName" label="任务名称" width="400" show-overflow-tooltip />
        <el-table-column prop="author" label="作者" width="180" />
        <el-table-column prop="createTime" label="提交时间" />
        <el-table-column prop="status" label="录入状态">
          <template #default="{ row }">
            <span :class="[row.humanInput === '0' ? 'text-[#d10000]' : 'text-active']">
              {{ row.humanInput === '0' ? '未录入' : '已录入' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button type="text" @click="openDialog(row)">
              人工录入
            </el-button>
            <el-button type="text" @click="(e) => download({ row }, e)">
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="w-full mx-[10%] flex justify-end mt-[20px]">
      <el-pagination
        v-model:current-page="pageInfo.current"
        v-model:page-size="pageInfo.size"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <el-dialog v-model="dialogTableVisible" title="人工录入" width="1200" @close="closeDialog">
    <el-table :data="fillList">
      <el-table-column property="serialNo" label="序号" width="100" />
      <el-table-column property="format" label="检测模块" />
      <el-table-column property="checkDesc" label="检测项描述" />
      <el-table-column property="problemDescription" label="问题描述" />
      <el-table-column property="originalText" label="原文内容举例">
        <template #default="{ row }">
          <el-input v-model="row.originalText" size="small" type="textarea" style="width: 200px;" />
        </template>
      </el-table-column>
      <el-table-column property="pageNum" label="对应页码">
        <template #default="{ row }">
          <el-input v-model="row.pageNum" size="small" style="width: 100px;" />
        </template>
      </el-table-column>
      <!-- <el-table-column property="problemDescription" label="问题描述">
        <template #default="{ row }">
          <el-input v-model="row.problemDescription" size="small" type="textarea" />
        </template>
      </el-table-column> -->
    </el-table>
    <div class="w-full flex justify-end mt-[20px]">
      <el-button @click="cancel">
        取消
      </el-button>
      <el-button type="primary" @click="save">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<style scoped>
.bg-container {
  background: linear-gradient(180deg, rgba(104, 232, 216, 0.35), rgba(55, 204, 176, 0));
}
</style>