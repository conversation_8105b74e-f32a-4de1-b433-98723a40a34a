<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/40文件/WORD</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="✅论文检测" transform="translate(-196.000000, -555.000000)">
            <g id="编组-3" transform="translate(140.000000, 281.000000)">
                <g id="icon/40文件/WORD" transform="translate(56.000000, 274.000000)">
                    <rect id="_mockplus_fix_" x="0" y="0" width="40" height="40"></rect>
                    <rect id="矩形" fill="#000000" fill-rule="nonzero" opacity="0" x="0" y="0" width="40" height="40"></rect>
                    <path d="M8,0 L26.6666797,0 L37.3333203,10.6666797 L37.3333203,34.6666797 C37.3333203,37.6 34.9333203,40 32,40 L8,40 C5.06667969,40 2.66667969,37.6 2.66667969,34.6666797 L2.66667969,5.33332031 C2.66667969,2.4 5.06667969,0 8,0 Z" id="路径" fill="#4297FC" fill-rule="nonzero"></path>
                    <path d="M20,23.7333203 L22.6666797,31.2 C23.2,32.2666797 24.8,32.2666797 25.0666742,31.2 L29.0666742,20.5333203 C29.3333203,19.7333203 29.0666742,19.2 28.2666797,18.9333203 C27.4666797,18.6666406 26.9333203,18.9333203 26.6666797,19.4666797 L24,26.9333203 L21.3333203,19.4666797 C20.8,18.4 19.2,18.4 18.6666797,19.4666797 L16,26.9333203 L13.3333203,19.4666797 C13.0666742,18.9333203 12.2666797,18.4 11.4666797,18.6666797 C10.6666797,18.9333594 10.4,19.7333203 10.6666797,20.5333203 L14.6666797,31.2 C15.2,32.2666797 16.8,32.2666797 17.0666742,31.2 L20,23.7333203 Z" id="路径" fill="#FFFFFF" fill-rule="nonzero"></path>
                    <path d="M26.6666797,0 L37.3333203,10.6666797 L29.3333203,10.6666797 C27.7333203,10.6666797 26.6666797,9.6 26.6666797,8 L26.6666797,0 Z" id="路径" fill="#004789" fill-rule="nonzero" opacity="0.619"></path>
                </g>
            </g>
        </g>
    </g>
</svg>