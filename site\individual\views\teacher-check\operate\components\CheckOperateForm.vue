<script setup lang="ts">
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { updatePaperReviewApi } from '../../../../service/api'

const props = defineProps<{
  parentData: any
}>()

const emits = defineEmits(['submitFinish'])

const form = reactive({
  approvalStatus: '', // 审核结果 2 不通过 3 通过
  reason: '', // 审核理由
})

function onSubmit() {
  if (!form.approvalStatus) {
    ElMessage({
      message: '请选择审核结果',
      type: 'warning',
    })
    return
  }
  ElMessageBox.confirm(
    '确认提交您的审核结果？',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'success',
    },
  )
    .then(() => {
      const teacherModalLoadingService = ElLoading.service({
        fullscreen: true,
      })
      
      updatePaperReviewApi({
        id: props.parentData?.id,
        userId: props.parentData?.userId,
        titleCn: props.parentData?.titleCn,
        approvalStatus: form.approvalStatus,
        reason: form.reason,
      }).then((res) => {
        if (res.data.code === 200) {
          emits('submitFinish')
        }
        else {
          ElMessage({
            message: res.data.message,
            type: 'error',
          })
          console.error('提交审核结果失败===>', res)
          emits('submitFinish')
        }
      }).catch((error) => {
        console.error('提交审核结果失败===>', error)
        ElMessage({
          message: '提交审核结果失败，请稍后重试',
          type: 'error',
        })
        emits('submitFinish')
      }).finally(() => {
        teacherModalLoadingService.close()
      })
    })
    .catch(() => {
      emits('submitFinish')
    })
}
</script>

<template>
  <el-form class="w-full">
    <el-form-item label="审核结果">
      <el-radio-group v-model="form.approvalStatus">
        <el-radio 
          :border="true"
          class="
            !flex !flex-row-reverse !justify-end
            !py-6
            !pl-0 !pr-4
          " 
          :class="{
            '!bg-[#edf9f9a0]': form.approvalStatus === '3',
          }"
          value="3"
        >
          <div 
            class="
              flex items-center gap-2 mr-2
            "
          >
            <img 
              v-if="form.approvalStatus === '3'"
              class="w-[40px] h-[40px]"
              src="@individual/assets/image/icon_校对_正确选中.webp"
            >
            <img 
              v-else
              class="w-[40px] h-[40px]"
              src="@individual/assets/image/icon_校对_正确未选中.webp"
            >
            <span class="text-base">审核通过</span>
          </div>
        </el-radio>
        <el-radio 
          :border="true"
          class="
            !flex !flex-row-reverse !justify-end
            !py-6
            !pl-0 !pr-4
          " 
          :class="{
            '!bg-[#edf9f9a0]': form.approvalStatus === '2',
          }"
          value="2"
        >
          <div 
            class="
              flex items-center gap-2 mr-2
            "
          >
            <img 
              v-if="form.approvalStatus === '2'"
              class="w-[40px] h-[40px]"
              src="@individual/assets/image/icon_校对_错误选中.webp"
            >
            <img 
              v-else
              class="w-[40px] h-[40px]"
              src="@individual/assets/image/icon_校对_错误未选中.webp"
            >
            <span class="text-base">审核不通过</span>
          </div>
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="审核理由">
      <el-input 
        v-model="form.reason" 
        type="textarea" 
        show-word-limit
        :maxlength="500"
        style="width: 688px"
        :autosize="{
          minRows: 4,
        }"
      />
    </el-form-item>
    
    <el-form-item>
      <el-button 
        type="primary" 
        size="large"
        :class="
          cn(
            'hover:opacity-80 !border-0 bg-gradient-to-r from-[#6ED86E] to-[#1DB989]',
            'w-[80px] mx-auto',
          )
        "
        @click="onSubmit"
      >
        提交
      </el-button>
    </el-form-item>
  </el-form>
</template>

<style scoped>

</style>
