<script setup lang="ts">
import { getPaperDetail } from '../../service/api/sys'
// import FeedBackBar from '../record/components/feedback-bar.vue'
import ListHeader from '../teacher-check/components/ListHeader.vue'
import Table from '../teacher-check/components/Table.vue'
import Title from '../teacher-check/components/Title.vue'
// import CheckResultInfo from './components/CheckResultInfo.vue'
import PaperBaseInfo from './components/PaperBaseInfo.vue'
import ReportInfo from './components/ReportInfo.vue'

const props = defineProps<{
  previewRow: any
}>()

const reportDetail = ref<any>(null)

initReportInfo()
const anchor = useTemplateRef<any>('anchor')
const feedBackRef = useTemplateRef<any>('feedBackRef')

async function initReportInfo() {
  const res = await getPaperDetail({
    id: props.previewRow.id,
  })
  reportDetail.value = res.data.data
}

provide('reportDetail', reportDetail)

function scrollToTop() {
  anchor.value.scrollIntoView({ behavior: 'smooth' })
}
function openFeedBackDialog() {
  feedBackRef.value.openFeedBackDialog()
}
function clearFeedBackContent() {
  feedBackRef.value.clear()
}
watch(() => props.previewRow, () => {
  initReportInfo()
})
defineExpose({
  scrollToTop,
  clearFeedBackContent, 
  openFeedBackDialog,
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6 report-container relative">
    <div ref="anchor" />
    <Title 
      class="mt-[30px]"
      :title="reportDetail && reportDetail.titleCn" 
      time="2025-01-13 12:57:23"
    />
    <div class="max-w-6xl mx-auto rounded-lg p-8 relative">
      <img src="../../assets/svg/report-left-top.svg" class="w-[260px] absolute left-[-2.1%] top-[-3.8%]">
      <img src="../../assets/svg/report-right-top.svg" class="h-[140px] absolute right-0 top-[-3.9%]">
      <!-- <FeedBackBar ref="feedBackRef" class="w-full absolute left-0 top-[-70px]" :preview-row="previewRow" /> -->
      <!-- Title Section -->
      <!-- 审核结果 -->
      <!-- <div v-if="reportDetail && reportDetail.reviewResult && !['0', '1'].includes(reportDetail.approvalStatus)" class="mb-6 px-[30px] py-[20px] bg-[#fff] relative z-2 rounded-[8px]">
        <div>
          <ListHeader
            chinese="审核结果"
            english="AUDIT RESULT"
          />
          <CheckResultInfo
            :check-result="{
              status: '通过',
              checker: '余华',
              checkTime: '2025-01-011 09:00:45',
              comments: '标题、章节层级不规范，如第二章节第2句话不正确。请仔细检查！...',
            }"
          />
        </div>
      </div> -->
      <!-- 论文基本信息 -->
      <div class="mb-6 px-[30px] py-[20px] bg-[#fff] relative z-2 rounded-[8px] pb-8">
        <img src="../../assets/svg/clasp.svg" class="w-[44px] absolute  bottom-[-50px] z-10">
        <img src="../../assets/svg/clasp.svg" class="w-[44px] absolute  bottom-[-50px] right-[50px] z-10">

        <ListHeader
          chinese="论文信息"
          english="Paper Information"
        />
        <PaperBaseInfo
          :paper-info="{
            title: '张三三的毕业论文',
            author: '张三三',
            school: '中国科学院大学',
            degree: '硕士',
            totalPages: 50,
            chineseChars: 80096,
            englishChars: 396,
            wordCount: 80378,
          }"
        />
      </div>

      <!-- Test Report Section -->
      <div class="mb-6 px-[30px] py-[20px] bg-[#fff] relative z-2 rounded-[8px] ">
        <img src="../../assets/svg/clasp.svg" class="w-[44px] absolute  bottom-[-50px] z-10">
        <img src="../../assets/svg/clasp.svg" class="w-[44px] absolute  bottom-[-50px] right-[50px] z-10">

        <ListHeader
          chinese="检测结果"
          english="TEST REPORT"
        />

        <!-- <div class="w-full flex flex-row justify-between items-center my-6">
          <div class="flex-1 flex items-center gap-9">
            <span class="label">格式错误</span>
            <span class="content">{{ reportDetail && reportDetail.reviewResult?.formatError || '0' }}项</span>
          </div>
          <div class="flex-1 flex items-center gap-9">
            <span class="label">语义偏差</span>
            <span class="content">{{ reportDetail && reportDetail.reviewResult?.semanticDeviation || '0' }}项</span>
          </div>
        </div>
        <div class="w-full flex flex-row justify-between items-center my-6">
          <div class="flex-1 flex items-center gap-9">
            <span class="label">优化建议</span>
            <span class="content">{{ reportDetail && reportDetail.reviewResult?.improvementSuggestions || '0' }}项</span>
          </div>
          <div class="flex-1 flex items-center gap-9">
            <span class="label">问题总数</span>
            <span class="content">{{ reportDetail && reportDetail.reviewResult?.totalErrorNum || '0' }}项</span>
          </div>
        </div> -->
        <!-- 格式错误 -->
        <Table type="summary">
          <template #header>
            <div class="w-full h-full flex items-center ">
              <div class="flex flex-row items-center gap-4">
                <span class="text-[16px] !text-black">格式错误</span>
              </div>
            </div>
          </template>
        </Table>
        <div class="w-full h-6" />
        <!-- 语义偏差 -->
        <Table type="semantic"> 
          <template #header>
            <div class="w-full h-full flex items-center ">
              <div class="flex flex-row items-center gap-4 ">
                <span class="text-[16px] !text-black">语义偏差</span>
                <span class="text-sm font-normal text-gray-500" />
              </div>
            </div>
          </template>
        </Table>
        <div class="w-full h-6" />
        <!-- 优化建议 -->
        <Table type="abstract"> 
          <template #header>
            <div class="w-full h-full flex items-center ">
              <div class="flex flex-row items-center gap-4 ">
                <span class="text-[16px] !text-black">优化建议</span>
                <span class="text-sm font-normal text-gray-500" />
              </div>
            </div>
          </template>
        </Table>
      </div>

      <!-- 报告说明 -->
      <div class="mb-6 px-[30px] py-[20px] bg-[#fff] relative z-2 rounded-[8px]">
        <ListHeader
          chinese="报告说明"
          english="REPORT DESCRIPTION"
        />

        <ReportInfo />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.label {
  font-size: 14px;
  width: 84px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
}
.content {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}
.report-container {
  background: linear-gradient(180deg, rgba(104, 232, 216, 0.35), rgba(55, 204, 176, 0));
}
</style>
