<script setup lang="ts">
interface PaperInfo {
  title?: string
  author?: string
  school?: string
  degree?: string
  totalPages?: number
  chineseChars?: number
  englishChars?: number
  wordCount?: number
}

defineProps<{
  paperInfo?: PaperInfo
}>()
</script>

<template>
  <div class="w-full">
    <!-- 第一行 -->
    <div class="w-full flex flex-row justify-between items-center">
      <!-- 论文题目 -->
      <div class="flex-1 flex items-center gap-9">
        <span class="label">论文题目</span>
        <span class="content">{{ paperInfo?.title || '张三三的毕业论文' }}</span>
      </div>
      <!-- 作者 -->
      <div class="flex-1 flex items-center gap-9">
        <span class="label">作者</span>
        <span class="content">{{ paperInfo?.author || '张三三' }}</span>
      </div>
    </div>

    <!-- 第二行 -->
    <div class="w-full flex flex-row justify-between items-center mt-7">
      <!-- 学校 -->
      <div class="flex-1 flex items-center gap-9">
        <span class="label">学校</span>
        <span class="content">{{ paperInfo?.school || '中国科学院大学' }}</span>
      </div>
      <!-- 学位 -->
      <div class="flex-1 flex items-center gap-9">
        <span class="label">学位</span>
        <span class="content">{{ paperInfo?.degree || '硕士' }}</span>
      </div>
    </div>

    <!-- 第三行 -->
    <div class="w-full flex flex-row justify-between items-center mt-7">
      <!-- 全文页数 -->
      <div class="flex-1 flex items-center gap-9">
        <span class="label">全文页数</span>
        <span class="content">{{ paperInfo?.totalPages || '50' }}页</span>
      </div>
      <!-- 字数统计 -->
      <div class="flex-1 flex items-center gap-9">
        <span class="label">字数统计</span>
        <span class="content">{{ paperInfo?.wordCount || '80,378' }}字</span>
      </div>
    </div>

    <!-- 第四行 -->
    <div class="w-full flex flex-row justify-between items-center mt-7">
      <!-- 中文字符 -->
      <div class="flex-1 flex items-center gap-9">
        <span class="label">中文字符</span>
        <span class="content">{{ paperInfo?.chineseChars || '80,096' }}个</span>
      </div>
      <!-- 英文字符 -->
      <div class="flex-1 flex items-center gap-9">
        <span class="label">英文字符</span>
        <span class="content">{{ paperInfo?.englishChars || '396' }}个</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.label {
  font-size: 14px;
  width: 84px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
}
.content {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}
</style>
