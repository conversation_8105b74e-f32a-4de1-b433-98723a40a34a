<script setup lang="ts">
import PersonalRegister from './personal-register.vue'

enum REGISTERTYPE {
  PERSONAL = 'personal',
}
const loginType = ref<REGISTERTYPE>(REGISTERTYPE.PERSONAL)

function changeLoginType(type: REGISTERTYPE) {
  loginType.value = type
}
</script>

<template>
  <div class="w-[40%] bg-[rgba(255,255,255,0.70)] overflow-hidden flex justify-center content-start relative flex-wrap base:pt-[195px] s-base:pt-[135px]">
    <div class="w-[380px] relative flex flex-wrap justify-start">
      <div class="w-full flex flex-nowrap">
        <!-- :class="{ 'tab-item-active': loginType === REGISTERTYPE.PERSONAL }"  -->
        <div class="tab-item" @click="changeLoginType(REGISTERTYPE.PERSONAL)">
          个人注册
        </div>
      </div>

      <PersonalRegister v-if="loginType === REGISTERTYPE.PERSONAL" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tab-item {
  cursor: pointer;
  box-sizing: border-box;
  font-size: 24px;
  font-family:
    PingFang SC,
    PingFang SC-Medium;
  font-weight: Medium;
  text-align: left;
  color: rgba(0, 0, 0, 0.85);
}
.tab-item-active {
  cursor: pointer;
  box-sizing: border-box;
  font-size: 24px;
  line-height: 20px;
  position: relative;
  color: #06b190;
  &::after {
    position: absolute;

    height: 2px;
    box-sizing: border-box;
    width: 100%;
    content: '';
    display: inline-block;
    background-color: #06b190;
    top: 160%;
    left: 0;
  }
}
:deep(.el-input__wrapper) {
  border-radius: 8px;
}
</style>