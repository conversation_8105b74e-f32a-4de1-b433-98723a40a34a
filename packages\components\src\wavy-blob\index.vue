<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from 'vue'

// 定义 props
interface Props {
  progress?: number
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  progress: 45,
  color: 'linear-gradient(135deg, #22a3ee 0%, #ffb6d4 50%, #0891b2 100%)',
})

// 容器引用和高度
const batteryBodyRef = ref<HTMLElement>()
const containerHeight = ref(320) // 默认高度

// 计算波浪的底部位置（像素值）
const waveBottomPixels = computed(() => {
  // 重新调整计算：让波浪动画与进度完全匹配
  const progressRatio = props.progress / 100
  // 0%: 波浪圆心在底部-90%位置（刚好露出波浪顶部）
  // 100%: 波浪圆心在顶部+10%位置（完全充满容器）
  const bottomPercentage = 0 + progressRatio * 100 // 从-90%到10%
  return (containerHeight.value * bottomPercentage) / 100
})

// 获取容器高度
async function updateContainerHeight() {
  await nextTick()
  if (batteryBodyRef.value) {
    containerHeight.value = batteryBodyRef.value.clientHeight
  }
}

// 组件挂载后获取容器高度
onMounted(() => {
  updateContainerHeight()
  // 监听窗口大小变化
  window.addEventListener('resize', updateContainerHeight)
})
</script>

<template>
  <div class="battery-container">
    <div ref="batteryBodyRef" class="battery-body">
      <div
        class="wave"
        :style="{
          '--wave-bottom-px': `${waveBottomPixels}px`,
          '--progress': props.progress,
          '--wave-color': props.color,
        }"
      />
    </div>
  </div>
</template>

<style scoped>
.battery-container {
  width: 100%;
  height: 320px;
  position: relative;
  background: #f8fafc;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.battery-body {
  width: 100%;
  height: 100%;
  position: relative;
  border: 3px solid #e2e8f0;
  border-radius: 16px;
  background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
  overflow: hidden;
}

.wave {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--wave-color);
  transition: all 0.3s ease-out;
  opacity: calc(0.3 + var(--progress) * 0.007);
}

/* 第一个波浪 blob */
.wave::before {
  content: '';
  position: absolute;
  width: 200%;
  aspect-ratio: 1;
  bottom: var(--wave-bottom-px);
  left: 50%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 45%;
  transform: translateX(-50%) rotate(0deg);
  animation: wave-rotate-1 6s linear infinite;
  transition: bottom 0.3s ease-out;
  z-index: 1;
}

/* 第二个波浪 blob */
.wave::after {
  content: '';
  position: absolute;
  width: 200%;
  aspect-ratio: 1;
  bottom: var(--wave-bottom-px);
  left: 50%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 47%;
  transform: translateX(-50%) rotate(0deg);
  animation: wave-rotate-2 10s linear -3s infinite;
  transition: bottom 0.3s ease-out;
  z-index: 2;
}

/* 波浪动画 - 第一个blob */
@keyframes wave-rotate-1 {
  0% {
    transform: translateX(-50%) rotate(0deg);
  }
  50% {
    transform: translateX(-50%) rotate(180deg);
  }
  100% {
    transform: translateX(-50%) rotate(360deg);
  }
}

/* 波浪动画 - 第二个blob */
@keyframes wave-rotate-2 {
  0% {
    transform: translateX(-50%) rotate(0deg);
  }
  50% {
    transform: translateX(-50%) rotate(180deg);
  }
  100% {
    transform: translateX(-50%) rotate(360deg);
  }
}

/* 充电完成发光效果 */
@keyframes glow-pulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}
</style>
