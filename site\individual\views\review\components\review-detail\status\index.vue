<script setup lang="ts">
import Slogan from '../../slogan'
import TemplateSelect from '../template-select/index.vue'
import FeedBackStatus from './feedback'
import OriginalStatus from './original-status.vue'

enum STATUS {
  ORIGINALSTATUS = -1,
  QUEUING = 0,
  CHECKING = 1,
  SUCCESS = 2,
  FAILURE = 3,
  TEMPLATSTATUS = 9,
}
const status = ref(STATUS.ORIGINALSTATUS)

const frontTempFileInfo = ref(null)

function setStatus(val: number) {
  status.value = val
}
function setFrontTempFileInfo(val: any) {
  frontTempFileInfo.value = val
}
provide('useStatus', { status, setStatus })
provide('useFrontTempFileInfo', { frontTempFileInfo, setFrontTempFileInfo })
provide('STATUS', STATUS)
</script>

<template> 
  <Slogan :class="status === STATUS.TEMPLATSTATUS ? ['s-base:mt-[300px]', 'base:mt-[0]'] : ''" /> 
  <div class="relative w-[1160px] min-h-[400px] rounded-[8px] overflow-hidden" :class="status === STATUS.TEMPLATSTATUS ? ['s-base:mb-[80px] ', 'base:mb-[0]'] : ''">
    <OriginalStatus v-if="status === STATUS.ORIGINALSTATUS" />
    <TemplateSelect v-else-if="status === STATUS.TEMPLATSTATUS" /> 
    <FeedBackStatus v-else />
  </div>
</template>

<style scoped>

</style>