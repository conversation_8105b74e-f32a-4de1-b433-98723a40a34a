import { request } from '../request'

export function generateCaptcha(data: any) {
  return request({
    url: '/user/generateCaptcha',
    method: 'post',
    data,
  })
}

export function register(data: any) {
  return request({
    url: '/user/register',
    method: 'post',
    data,
  })
}

export function loginByCaptcha(data: any) {
  return request({
    url: '/user/loginByCaptcha',
    method: 'post',
    data,
  })
}

export function verifyToken() {
  return request({
    url: '/user/verifyToken',
    method: 'post',
  })
}