<script setup lang="ts">
import { Check, Close, Delete, Download, Search } from '@element-plus/icons-vue'
import { css } from '@emotion/css'
import { Modal } from '@paper-review/components'
import day from 'dayjs'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import debounce from 'lodash/debounce'
import { toRaw } from 'vue'

import { deletePaperDetail, paperDownload } from '../../service/api/sys.ts'
// import ReportInfo from '../report/index.vue'
// @ts-ignore
import ReportInfo from '../report/temp-report.vue'
// @ts-ignore
import TeacherNameTable from './components/teacher-name-table/index.vue'
import { useTableList } from './index.ts'
import TableRecord from './table-record.tsx'

const viewType = ref('list')
const tableSelection = ref([])
const previewDialogVisible = ref(false)

const popoverCss = css`
  height: 32px;
  color: rgba(0,0,0,0.85);
  line-height: 22px;
  font-size: 14px;
  align-content: center;
  cursor: pointer;
  padding-left: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &:hover {
    color: #06b190;
    background: #eaf9f9;
  }
`
const filterState = reactive<any>({
  statusList: [],
  documentFormat: [],
  sortOrder: '',
  taskName: '',
  approvalStatus: [],
})
const loading = ref(false)
const previewRow = ref(null)
// @ts-ignore
const reportType = ref<'statistics' | 'comparison' >('statistics')
// @ts-ignore
const tableRecord = useTemplateRef('tableRecord')
const filterPopover = ref(false)
const rawFilterState = reactive<any>({
  statusList: [],
  sortOrder: '',
  taskName: '',
  approvalStatus: [],
})

const { getTableList, pageInfo } = useTableList()
const reportInfoRef = useTemplateRef<any>('reportInfoRef')

getTableList()

function switchViewType(type) {
  viewType.value = type
}
function select(data) {
  tableSelection.value = data
}

function switchSort(sort) {
  filterState.sortOrder = sort
}
function resetStatus() {
  filterState.statusList = []
  filterState.approvalStatus = []
  getTableList(filterState)
  filterPopover.value = false
}

function cancelFilter() {
  filterPopover.value = false
  Object.keys(rawFilterState).forEach((key) => {
    filterState[key] = rawFilterState[key]
  })
}

function filterList(filterState) {
  pageInfo.pageNum = 1
  getTableList(filterState)
  filterPopover.value = false
}

function goTop() {
  reportInfoRef.value.scrollToTop()
}

function feedback() {
  reportInfoRef.value.openFeedBackDialog()
}

async function batchDownload() {
  const fileUrls = tableSelection.value.map((item: any) => item.wordUrl).toString()
  try {
    const res = await paperDownload({
      fileUrls,
    })
    if (res.response.data.status !== 200) {
      ElMessage({
        message: '文件下载失败，请稍后重试',
        type: 'error',
      })
      return 
    }
    const date = day().format('YYYYMMDD')
    const blobObj = new Blob([res.data], { type: 'application/zip' })
    const url = window.URL.createObjectURL(blobObj)
    const a = document.createElement('a')
    a.href = url
    a.download = `论文检测系统_${date}.zip` 
    document.body.appendChild(a)
    a.click()
    a.remove()
    window.URL.revokeObjectURL(url)
  }
  catch (e) {
    ElMessage({
      type: 'error',
      message: '下载失败，请稍后再试！',
    })
  }
}
async function batchDelete() {
  loading.value = true
  const delteIds = tableSelection.value.map((item: any) => item.id)
  const res = await deletePaperDetail(delteIds)
  loading.value = false
  if (res.data.code === 200) {
    ElMessage({
      type: 'success',
      message: '删除成功',
    })
    tableSelection.value = []
    getTableList(filterState)
  }
}

function preview(row) {
  previewRow.value = row
  previewDialogVisible.value = true
}

function closePreview() {
  previewRow.value = null
  previewDialogVisible.value = false
  reportInfoRef.value.clearFeedBackContent()
}

const resetBtnDisabled = computed(() => {
  return !filterState.approvalStatus.length && !filterState.statusList.length
})

const badgeNumber = computed(() => {
  // return (filterState.statusList.length ? 1 : 0) + (filterState.approvalStatus.length ? 1 : 0)
  return (filterState.statusList.length) + (filterState.approvalStatus.length)
})

async function download(scope, e) {
  scope = { row: toRaw(previewRow.value) }
  if (scope.row.status !== '2') {
    ElMessage({
      message: '当前状态不可下载',
      type: 'warning',
    })
    return
  }
  e.stopPropagation()

  try {
    const res = await paperDownload({
      fileUrls: [scope.row.wordUrl].toString(),
    })
    if (res.response.data.status !== 200) {
      ElMessage({
        message: '文件下载失败，请稍后重试',
        type: 'error',
      })
      return
    }
    const date = day().format('YYYYMMDD')
    const blobObj = new Blob([res.data], { type: 'application/zip' })
    const url = window.URL.createObjectURL(blobObj)
    const a = document.createElement('a')
    a.href = url
    a.download = `论文检测系统_${date}.zip` 
    document.body.appendChild(a)
    a.click()
    a.remove()
    window.URL.revokeObjectURL(url)
  }
  catch (e) {
    ElMessage({
      type: 'error',
      message: '下载失败，请稍后再试！',
    })
  }
  finally {
    // endLoading()
  }
}
/** =============教师审核============== */
const teacherModalVisible = ref(false)
    
const currentTaskRow = ref()
// @ts-ignore
function handleSubmitDocumentToCheck(scope) {
  scope = { row: toRaw(previewRow.value) } 

  /**
   * 检测状态 0.排队中 1.检测中 2.已完成 3.检测失败
   * 审核状态 0.未提交 1.审核中 2.不通过 3.初步审核通过 4.最终审核通过
   * 1. 排队中/检测中/检测失败
   *  或者 审核状态通过/不通过 
   *  - 提交按钮变灰色，点击提示“当前状态不可提交审核”
   * 2. 已完成 审核中 - 提示 “该检测报告正在审核中，请问是否重复提交？【确认】【取消】”
   * 3. 已完成 未提交 - 调接口检测是否有相同论文
   *   3.1 有相同论文 - 提示“同篇论文有正在审核的检测报告，请问是否重复提交？【确认】【取消】”
   *   3.2 无相同论文 - 继续提交审核
   */

  // 1. 排队中/检测中/检测失败 或者 审核状态通过/不通过 不可提交
  if (
    scope.row.status === '0' 
    || scope.row.status === '1'
    || scope.row.status === '3'
    || scope.row.approvalStatus === '2'
    || scope.row.approvalStatus === '3'
    || scope.row.approvalStatus === '4'
  ) {
    ElMessage({
      message: '当前状态不可提交审核',
      type: 'warning',
    })
  }

  // 2. 已完成 审核中 - 提示 “该检测报告正在审核中，请问是否重复提交？【确认】【取消】”
  else if (
    scope.row.status === '2'
    && scope.row.approvalStatus === '1'
  ) {
    ElMessageBox.confirm(
      '该检测报告正在审核中，请问是否重复提交？',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
      .then(async () => {
        setTimeout(() => {
          currentTaskRow.value = scope.row
          teacherModalVisible.value = true
        }, 200)

        return 0
      })
      .catch(() => {
        // eslint-disable-next-line no-console
        console.log('取消提交')
        return 0
      })
  }

  // 3. 已完成 未提交 - 调接口检测是否有相同论文
  // else if (
  //   scope.row.status === '2'
  //   && scope.row.approvalStatus === '0'
  // ) {
  // 暂时不做 TODO
  // const res = await checkSamePaper({
  //   taskId: scope.row.id,
  // })
  // if (res.response.data.status === 200) {

  //   ElMessageBox.confirm(
  //     '同篇论文有正在审核的检测报告，请问是否重复提交？',
  //     {
  //       confirmButtonText: '确认',
  //       cancelButtonText: '取消',
  //       type: 'warning',
  //     },
  //   )
  //     .then(() => {
  //       // eslint-disable-next-line no-console
  //       console.log('继续提交')
  //     })
  //     .catch(() => {
  //       // eslint-disable-next-line no-console
  //       console.log('取消提交')
  //       return 0
  //     })
  // }
  // }

  // 4. 提交审核
  else {
    currentTaskRow.value = scope.row
    teacherModalVisible.value = true
  }
}

/**
 * 提交给教师审核
 */
const teacherNameTableRef = ref()

async function handleSubmitToTeacherCheck() {
  const teacherName = teacherNameTableRef.value?.currentRow?.teacherName
  const teacherEmail = teacherNameTableRef.value?.currentRow?.teacherEmail

  if (!teacherName || !teacherEmail) {
    ElMessage({
      message: '请先选择教师',
      type: 'warning',
    })
    return
  }

  // ======= 提交审核时，显示 loading 效果 =======
  const teacherModalLoadingService = ElLoading.service({
    target: '.teacher-check-custom-modal',
    fullscreen: false,
  })

  try {
    const res = await sendEmailReviewApi({
      ...currentTaskRow.value,
      teacherEmail,
      teacherName,
    })

    if (res.response.data.status === 200) {
      ElMessage({
        message: res.response.data.data.msg,
        type: 'success',
      })
      getTableList()
      teacherModalVisible.value = false
    }
    else {
      ElMessage({
        message: '提交审核失败',
        type: 'error',
      })
    }
  }
  catch (error) {
    ElMessage({
      message: '提交审核失败',
      type: 'error',
    })
  }
  finally {
    // ======= 提交审核时，关闭 loading 效果 =======
    teacherModalLoadingService.close()
  }
}
/** =============教师审核============== */
watch(() => filterPopover.value, (val) => {
  if (val) {
    Object.keys(filterState).forEach((key) => {
      rawFilterState[key] = filterState[key]
    })
  }
})
watch(() => filterState?.taskName, debounce(() => {
  getTableList(filterState)
}, 800))
watch([() => filterState?.sortOrder], () => {
  getTableList(filterState)
})
</script>

<template>
  <div class="w-full h-full flex flex-wrap justify-center content-start box-border px-[150px] py-[32px] overflow-auto scrollbar-thin">
    <div class="relative w-[1140px] flex flex-nowrap justify-between items-center mb-[16px] h-[40px]">
      <!-- Left -->
      <div class="w-[50%] flex flex-nowrap justify-start items-center mb-[16px] h-[40px]">
        <el-input v-model="filterState.taskName" clearable :prefix-icon="Search" placeholder="请输入任务名称" style="width: 44%;height: 40px;border-radius: 8px;margin-right: 8px" />
        <!-- <div class="bg-[#fff] flex justify-between w-[80px] h-[100%] border-[--el-border-color] border-[1px] rounded-[8px] mr-[8px] box-border">
          <div class="flex items-center justify-center box-border w-[38px] cursor-pointer rounded-[8px]" :class="{ 'bg-[#EAF9F9]': viewType === 'card' }" @click="switchViewType('card')">
            <img v-if="viewType === 'card'" src="@individual/assets/svg/menu-active.svg">
            <img v-else src="@individual/assets/svg/menu.svg">
          </div>
          <div class="flex items-center justify-center box-border w-[38px] cursor-pointer rounded-[8px] " :class="{ 'bg-[#EAF9F9]': viewType === 'list' }" @click="switchViewType('list')">
            <img v-if="viewType === 'list'" src="@individual/assets/svg/list-active.svg">
            <img v-else src="@individual/assets/svg/list.svg">
          </div>
        </div> -->
        <el-popover
          v-model:visible="filterPopover"
          popper-class="paper-record-popper"
          popper-style="min-width: 398px;"
          placement="bottom-end"
          :width="100"
          trigger="click"
        >
          <template #default>
            <div class="p-[24px] pb-[12px] text-[20px] text-[#000]">
              添加筛选条件
            </div>
            <div class="pl-[24px] text-[14px] pt-[6px] text-[rgba(0,0,0,0.45)]">
              论文类型
            </div>
            <el-checkbox-group v-model="filterState.documentFormat" style="width: 100%;padding:8px;padding-left: 24px;padding-bottom: 0px;">
              <el-checkbox value="word">
                Word
              </el-checkbox>
              <el-checkbox value="Latex">
                Latex
              </el-checkbox>
            </el-checkbox-group>
            <div class="pl-[24px] text-[14px] pt-[6px] text-[rgba(0,0,0,0.45)]">
              检测状态
            </div>
            <el-checkbox-group v-model="filterState.statusList" style="width: 100%;padding:8px;padding-left: 24px;padding-bottom: 0px;">
              <el-checkbox :value="2">
                已完成
              </el-checkbox>
              <el-checkbox :value="1" :disabled="filterState.approvalStatus.length">
                检测中
              </el-checkbox>
              <el-checkbox :value="0" :disabled="filterState.approvalStatus.length">
                排队中
              </el-checkbox>
              <el-checkbox :value="3" :disabled="filterState.approvalStatus.length">
                检测失败
              </el-checkbox>
            </el-checkbox-group>
            <!-- <div class="pl-[24px] pt-[12px] text-[14px] text-[rgba(0,0,0,0.45)]">
              审核状态
            </div>
            <el-checkbox-group v-model="filterState.approvalStatus" style="width: 100%;padding:8px;padding-left: 24px;padding-bottom: 0px;">
              <el-checkbox :value="0" :disabled="(filterState.statusList.length > 1 || (filterState.statusList.length && filterState.statusList[0] !== 2))">
                未提交
              </el-checkbox>
              <el-checkbox :value="1" :disabled="(filterState.statusList.length > 1 || (filterState.statusList.length && filterState.statusList[0] !== 2))">
                审核中
              </el-checkbox>
              <el-checkbox :value="3" :disabled="(filterState.statusList.length > 1 || (filterState.statusList.length && filterState.statusList[0] !== 2))">
                通过
              </el-checkbox>
              <el-checkbox :value="2" :disabled="(filterState.statusList.length > 1 || (filterState.statusList.length && filterState.statusList[0] !== 2))">
                不通过
              </el-checkbox>
            </el-checkbox-group> -->
            <div class="flex items-center py-[12px] px-[20px] w-[100%] justify-between">
              <el-button size="small" :disabled="resetBtnDisabled" @click="resetStatus()">
                重置
              </el-button>
              <div>
                <el-button size="small" class="bg-linear-gradient" @click="cancelFilter">
                  取消
                </el-button>
                <el-button size="small" class="bg-linear-gradient" style="border:none;color: #fff;;background-image: linear-gradient(90deg, #6ED86E, #1DB989);" @click="filterList(filterState)">
                  确认
                </el-button>
              </div>
            </div>
          </template>
          <template #reference>
            <div class="bg-[#fff] flex justify-between w-[40px] h-[100%] border-[--el-border-color] border-[1px] rounded-[8px] mr-[8px]">
              <div class="flex items-center justify-center box-border p-8px w-[100%] cursor-pointer">
                <el-badge v-show="badgeNumber !== 0 " :value="badgeNumber ">
                  <img v-show="filterState.statusList.length === 0" src="@individual/assets/svg/filter.svg">
                  <img v-show="filterState.statusList.length !== 0" src="@individual/assets/svg/filter-active.svg">
                </el-badge> 
                <div v-show="!badgeNumber">
                  <img v-show="filterState.statusList.length === 0" src="@individual/assets/svg/filter.svg">
                  <img v-show="filterState.statusList.length !== 0" src="@individual/assets/svg/filter-active.svg">
                </div>
              </div>
            </div>
          </template>
        </el-popover>
        <el-popover
          popper-class="paper-record-popper"
          popper-style="min-width: 150px;"
          placement="bottom-end"
          :width="100"
          trigger="click"
        >
          <template #default>
            <div :class="popoverCss" @click="switchSort('')">
              按提交时间倒序
              <Check v-show="filterState.sortOrder === ''" style="width: 15px;margin-right: 5px;color: #06b190;" />
            </div>
            <div :class="popoverCss" @click="switchSort('asc')">
              按提交时间升序
              <Check v-show="filterState.sortOrder === 'asc'" style="width: 15px;margin-right: 5px;color: #06b190;" />
            </div>
          </template>
          <template #reference>
            <div class="bg-[#fff] flex justify-between w-[40px] h-[100%] border-[--el-border-color] border-[1px] rounded-[8px]">
              <div class="flex items-center justify-center box-border p-8px w-[100%] cursor-pointer">
                <img src="@individual/assets/svg/sort.svg">
              </div>
            </div>
          </template>
        </el-popover>
      </div>
      <!-- Right -->
      <div v-show="tableSelection.length" class="w-[50%] flex flex-nowrap justify-end items-center mb-[16px] h-[40px]">
        <el-button :icon="Download" style="background: linear-gradient(90deg, #7ADC6A, #0FB48E);border: none; color:#fff;width: 100px;height: 40px;border-radius: 8px;font-size: 16px" @click="batchDownload"> 
          下载
        </el-button>
        <el-popconfirm
          title="确认删除?"
          @confirm="batchDelete"
        >
          <template #reference>
            <el-button :icon="Delete" style="width: 100px;height: 40px;border-radius: 8px;font-size: 16px;">
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </div>
    </div>
    <div v-loading="loading" class="w-[1140px] py-[20px] flex justify-center items-center rounded-[8px]" :class="viewType === 'list' ? ['px-[15px]', 'bg-[#fff]'] : ''">
      <TableRecord v-show="viewType === 'list'" ref="tableRecord" :view-type="viewType" :filter-state="filterState" :table-selection="tableSelection" @select="select" @preview="preview" />
      <!-- <CardRecord v-show="viewType === 'card'" :view-type="viewType" :filter-state="filterState" :table-selection="tableSelection" @select="select" @preview="preview" /> -->
    </div>

    <el-dialog v-model="previewDialogVisible" fullscreen :modal="false" :show-close="false">
      <div class="w-full min-w-[800px] h-[66px]  fixed top-0 bg-[#fff] border-b border-gray-200 flex items-center justify-between px-4 py-3 shadow-sm" style="z-index: 99;">
        <div class="font-medium text-gray-800 text-[18px] font-bold">
          查看报告
        </div>

        <!-- <div class="flex items-center space-x-6">
          <button class="text-gray-800 box-border w-[80px] hover:text-teal-600 text-sm font-medium" :class="reportType === 'statistics' ? 'active' : ''" style="margin-left: 190px;" @click="reportType = 'statistics'">
            统计报告
          </button>
          <button
            class="text-gray-800  box-border w-[80px] hover:text-teal-600  text-sm font-medium"
            :class="reportType === 'comparison' ? 'active' : ''" @click="reportType = 'comparison'"
          >
            比对报告
          </button>
        </div> -->

        <div class="flex items-center space-x-2">
          <!-- <button class="px-3 py-1 border border-gray-200 rounded text-sm text-gray-700 hover:bg-gray-50" @click="(e) => download(null, e)">
            下载报告
          </button> -->
          <!-- <button class="px-3 py-1 border border-gray-200 rounded text-sm text-gray-700 hover:bg-gray-50" style="margin-right: 60px;" @click="handleSubmitDocumentToCheck">
            提交审核
          </button> -->
          <button class="p-1 text-gray-500 hover:text-gray-700 mt-[5x]" @click="closePreview">
            <el-icon><Close /></el-icon>
          </button>
        </div>
      </div>   

      <div class="min-h-[800px] calc-[100vh-66px] bg-gray-100 mt-[66px]">
        <ReportInfo ref="reportInfoRef" :preview-row="previewRow" />  
      </div>
      <div class="fixed bottom-0 right-0 p-4">
        <div class="icon-round mb-[16px]" @click="goTop">
          <img src="../../assets/svg/goTop.svg">
        </div>
        <!-- <div class="icon-round">
          <img src="../../assets/svg/feedBack.svg" @click="feedback">
        </div> -->
      </div>
    </el-dialog>
  </div>

  <Modal
    v-model:dialog-visible="teacherModalVisible"
    width="400"
    class="teacher-check-custom-modal"
    title="提交审核"
  >
    <template #footer>
      <div>
        <el-button 
          type="primary" 
          :class="cn(
            'hover:opacity-80 !border-0 bg-gradient-to-r from-[#6ED86E] to-[#1DB989]',
            'w-[60px] h-[32px]',
          )"
          @click="() => handleSubmitToTeacherCheck()"
        >
          确认
        </el-button>

        <el-button 
          class="w-[60px] h-[32px]"
          @click="() => teacherModalVisible = false"
        >
          取消
        </el-button>
      </div>
    </template>
    <TeacherNameTable ref="teacherNameTableRef" />
  </Modal>
</template>

<style scoped lang="scss">
.icon-round {
  width: 48px;
  height: 48px;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 50%;
  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.bg {
  background-image: url('../../assets/svg/menu.svg');
  background-repeat: no-repeat;
}
.layout-header {
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
}
:deep(.el-badge__content.is-fixed) {
  right: 1px;
  top: -9px;
  background-image: linear-gradient(90deg, #6ed86e, #1db989);
}
</style>

<style lang="scss">
.el-dialog.is-fullscreen {
  padding: 0;
}
.el-dialog__header {
  padding: 0;
}
.paper-record-popper {
  margin-top: -5px;
  padding: 0 !important;
  .el-popper__arrow::before {
    display: none;
  }
}
:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #06b190;
}
</style>

<style lang="scss" scoped>
:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #06b190;
}
.active {
  box-sizing: border-box;
  background: #eaf9f9;
  color: #06b190;
  border-radius: 16px;
  padding: 3px 10px;
}
</style>