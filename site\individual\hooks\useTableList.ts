export function useTableList(
  cb: (params?: any) => Promise<any>,
) {
  const tableData = ref([])
  const totalCount = ref(0)

  const pageInfo = reactive({
    pageNum: 1,
    pageSize: 10,
  }) 
  const tableLoading = ref(false)

  const startLoading = () => {
    tableLoading.value = true
  }

  const endLoading = () => {
    tableLoading.value = false
  }

  const getTableList = async (params?: any) => {
    startLoading()

    // 0. 请求数据
    const res = await cb(params)

    // 1. 处理表格数据，这里暂时不用处理
    const result = res.data.data.records.map((item) => {
      return {
        ...item,
      }
    })

    // 2. 操作一些东西
    tableData.value = result
    totalCount.value = res.data.data.totalCount

    endLoading()
    
    // 3. 将列表数据返回
    return result
  }

  getTableList()

  return { 
    tableData, 
    totalCount, 
    tableLoading, 
    pageInfo, 
    getTableList, 
    startLoading, 
    endLoading,
  }
}