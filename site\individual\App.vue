<script>
</script>

<template>
  <router-view v-slot="{ Component }">
    <transition name="fade">
      <component :is="Component" />
    </transition>
  </router-view>
</template>

<style lang="scss" scoped>
.login-title {
  width: 48rem;
  height: 8.4rem;
  font-size: 5rem;
  font-family:
    PingFang SC,
    PingFang SC-Medium;
  font-weight: Medium;
  text-align: left;
  color: rgba(0, 0, 0, 0.85);
  align-content: center;
  line-height: 5rem;
  margin-left: 18rem;
}
.login-tip {
  width: 53.1rem;
  height: 2.5rem;
  font-size: 1.5rem;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: Regular;
  text-align: left;
  color: rgba(0, 0, 0, 0.65);
  line-height: 2.5rem;
  margin-top: 1.6rem;
  margin-left: 18rem;
}
.login-img {
  width: 44rem;
  height: 36rem;
  object-fit: contain;
  margin-left: 17rem;
  margin-top: 3.6rem;
}
</style>