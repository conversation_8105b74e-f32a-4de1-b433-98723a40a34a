export const calcRem = function () {
  function resize() {
    const baseFontSize = 10 // 1rem = 100px
    const designWidth = 1440 // 设计稿宽度
    const width = window.innerWidth // 屏幕宽度
    const cur = (width / designWidth) * baseFontSize
    let currentFontSize = cur
    if (cur < 10) {
      currentFontSize = 10
    }
    document.querySelector('html')!.style.fontSize = `${currentFontSize}px`
  }
  window.onresize = function () {
    resize()
  }
    
  document.addEventListener('DOMContentLoaded', resize)
}
