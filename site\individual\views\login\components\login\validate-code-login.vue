<script setup lang="ts">
import { Message } from '@element-plus/icons-vue'
import { Slide } from '@paper-review/components'
import { useSlide } from '../../../../hooks/use-slide/index'

const { PANNEL, switchPannel } = inject('switchPannel') as any
const { showSlide, verifyCodeMsg, disabledSendCode, slideToSended, cleanSlide } = useSlide()
const { formRef, slideRef, ruleForm, rules, submit, cleanVerifyCodeForm, sendCode, manualTriggerSendCode, slideSuccees } = useVerifyCodeLogin()

const router = useRouter()

function switchLoginPannel() {
  ruleForm.code = ''
  cleanSlide()
  switchPannel(PANNEL.REGISTER)
}
onUnmounted(() => {
  formRef.value?.resetFields()
  cleanVerifyCodeForm()
})
function viewPolicy(e) {
  e.stopPropagation()
  window.open(router.resolve({ name: 'policy' }).href, '_blank')
}
function viewPrivacy(e) {
  e.stopPropagation()
  window.open(router.resolve({ name: 'privacy' }).href, '_blank')
}
function trimInput() {
  ruleForm.code = ruleForm.code.trim()
}
</script>

<template>
  <div class="w-full flex mt-[70px]">
    <el-form ref="formRef" class="w-[372px]" :model="ruleForm" :rules="rules" hide-required-asterisk> 
      <el-form-item prop="account">
        <el-input v-model="ruleForm.account" maxlength="64" :prefix-icon="Message" autocomplete="off" class="h-[52px]" placeholder="邮箱或手机号" />
      </el-form-item>
      <el-form-item v-if="showSlide" prop="slideVerify">
        <Slide ref="slideRef" @success="slideSuccees" />
      </el-form-item>
      <el-form-item v-else prop="code">
        <div class="w-full flex justify-between">
          <el-input v-model="ruleForm.code" maxlength="6" autocomplete="off" class="h-[52px]" placeholder="验证码" style="width: 65%;" @input="trimInput">
            <template #prefix>
              <img src="../../../../assets/svg/guard.svg" style="margin-left: -2px;">
            </template>
          </el-input>
          <el-button
            type="primary"
            :disabled="disabledSendCode"  
            class="box-border w-full p-0 m-0" 
            style="background: linear-gradient(90deg, #6ED86E, #1DB989);border: none;height: 50px;width: 30%;"
            :style="{ opacity: disabledSendCode ? 0.5 : 1 }"
            @click="manualTriggerSendCode(1)"
          >
            {{ verifyCodeMsg }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item prop="readed" class="relative">
        <div class="w-full flex justify-between">
          <el-checkbox v-model="ruleForm.readed" name="type">
            <span class="text-[rgba(37,43,58,0.85)] text-[12px]">我已阅读并同意<span class="text-[#06B190]" @click="viewPolicy">法律声明、</span><span class="text-[#06B190]" @click="viewPrivacy">隐私政策</span></span>
          </el-checkbox>
          <el-checkbox v-model="ruleForm.autoLogin" name="type">
            <span class="text-[rgba(37,43,58,0.85)] text-[12px]">30天内自动登录</span>
          </el-checkbox>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" class="box-border w-full p-0 m-0" style="background: linear-gradient(90deg, #6ED86E, #1DB989);border: none;height: 50px;" @click="submit">
          登录
        </el-button>
      </el-form-item>
      <el-form-item>
        <div class="w-full">
          <span class="text-[#06B190] float-right cursor-pointer" @click="switchLoginPannel">没有账号？去注册</span>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
