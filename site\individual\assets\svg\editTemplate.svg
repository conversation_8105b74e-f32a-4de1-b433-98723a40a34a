<?xml version="1.0" encoding="UTF-8"?>
<svg width="203px" height="226px" viewBox="0 0 203 226" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 14</title>
    <defs>
        <path d="M157.069307,0 L200.069307,35 L167.819228,35 C161.369212,35 157.069307,31.4999615 157.069307,26.249968 L157.069307,0 Z" id="path-1"></path>
        <filter x="-20.9%" y="-14.3%" width="132.6%" height="140.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="-2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.201759178 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="✅论文检测-补充信息" transform="translate(-212.000000, -479.000000)">
            <g id="编组-12" transform="translate(140.000000, 217.000000)">
                <g id="编组-4" transform="translate(48.000000, 244.000000)">
                    <g id="编组-14" transform="translate(24.000000, 20.000000)">
                        <path d="M200,35.1939992 C200,76.3669862 200,138.126467 200,220.472441 C200,222.420658 198.053412,224 195.652174,224 L4.34782609,224 C1.94658804,224 0,222.420658 0,220.472441 L0,3.52755906 C0,1.57934198 1.94658804,0 4.34782609,0 C72.0408793,0 122.810669,0 156.657196,0" id="路径" fill="#FFFFFF"></path>
                        <g id="路径" fill-rule="nonzero">
                            <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                            <use fill="#FFFFFF" xlink:href="#path-1"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>