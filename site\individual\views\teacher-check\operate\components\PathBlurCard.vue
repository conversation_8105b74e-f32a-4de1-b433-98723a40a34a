<script setup lang="ts">
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { paperDownloadApi } from '../../../../service/api'

withDefaults(defineProps<{
  title?: string
  author?: string
  university?: string
  degree?: string
  previewUrl?: string
  downloadUrl?: string
}>(), {
  title: '王新元《面向容器编排平台的自动化调优系统研究》',
  author: '张三',
  university: '清华大学',
  degree: '硕士',
  previewUrl: 'https://www.baidu.com',
  downloadUrl: 'https://www.baidu.com',
})

async function handleDownload(fileUrl: string) {
  try {
    const res = await paperDownloadApi({
      fileUrls: fileUrl,
    })
    if (res.response.data.status !== 200) {
      ElMessage({
        message: '文件下载失败，请稍后重试',
        type: 'error',
      })
    }
    const date = dayjs().format('YYYYMMDD')
    const blobObj = new Blob([res.data], { type: 'application/zip' })
    const url = window.URL.createObjectURL(blobObj)
    const a = document.createElement('a')
    a.href = url
    a.download = `论文检测系统_${date}.zip` 
    document.body.appendChild(a)
    a.click()
    a.remove()
    window.URL.revokeObjectURL(url)
  }
  catch (e) {
    ElMessage({
      type: 'error',
      message: '下载失败，请稍后再试！',
    })
  }
}

function handlePreview(url: string) {
  window.open(url, '_blank')
}
</script>

<template>
  <div 
    class="
      w-full h-[112px] rounded-lg
      bg-gradient-to-t from-[#FFFAFA]/0 to-[#eef9f9]
      flex flex-row items-center gap-3 px-6 mb-6
      relative z-[10]
    "
  >
    <img 
      class="flex-shrink-0 w-16 h-16"
      src="@individual/assets/image/icon_HTML.webp"
    >

    <div class="flex-1 h-full flex flex-col justify-center gap-2 overflow-hidden">
      <el-tooltip 
        placement="top"
        :show-after="1000"
        content="有关人类的未来有关人类的未来有关人类的未来有关人类的未来有关人类的未来有关人类的未来"
      >
        <span class="text-[18px] !text-black truncate cursor-default">
          {{ title }}
        </span>
      </el-tooltip>
      
      <div class="text-[12px] font-normal opacity-45 flex flex-row flex-wrap">
        <span class="border-r border-[#D9D9D9] pr-3">{{ author }}</span>
        <span class="border-r border-[#D9D9D9] px-3">{{ university }}</span>
        <span class="pl-3">{{ degree }}</span>
      </div>
    </div>

    <div 
      aria-label="操作按钮"
      class="
        flex-shrink-0
        w-fit h-full
        flex justify-center items-center
        flex-col gap-1
        sm:flex-col sm:gap-1
        md:flex-row md:gap-3
        lg:flex-row lg:gap-3
      "
    >
      <el-button plain round @click="handlePreview(previewUrl)">
        在线预览
      </el-button>
      <div />
      <el-button plain round @click="handleDownload(downloadUrl)">
        离线下载
      </el-button>
    </div>

    <img 
      class=" absolute top-0 left-0 w-full h-full z-[-1]" 
      src="@individual/assets/image/blur-card-image.webp"
    >
  </div>
</template>
