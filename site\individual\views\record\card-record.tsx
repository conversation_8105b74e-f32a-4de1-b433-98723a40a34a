import { Delete, Download, View } from '@element-plus/icons-vue'
import { css } from '@emotion/css'
import { FileType, Modal } from '@paper-review/components'
import day from 'dayjs'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import Empty from '../../assets/svg/empty.svg'
import { deletePaperDetail } from '../../service/api/sys'
import TeacherNameTable from './components/teacher-name-table/index.vue'
import { useTableList } from './index'

const cardContainerCSS = css`
  &:hover {
    .card-check-container {
      opacity: 1;
    }

    .status-flag {
      opacity: 0;
    }
    
    .operation-bar {
      opacity: 1;
    }
  }
`
const statustMap = {
  0: {
    text: '排队中',
    color: '#c1c1c1',
  },
  1: {
    text: '检测中',
    color: '#5478ee',
  }, 
  2: {
    text: '已完成',
    color: '#52c41a',
  }, 
  3: {
    text: '检测失败',
    color: '#ff4d4f',
  },
}
export default defineComponent({
  name: '<PERSON>R<PERSON>ord',
  emits: ['select'],
  props: {
    filterState: Object,
    tableSelection: Array,
    viewType: String,
  },
  setup(props, { emit }) {
    const { tableData, getTableList, pageInfo } = useTableList()

    const select = (item, e?: Event) => {
      e?.stopPropagation()
      item.checked = !item.checked
      const selection = tableData.value.filter((item: any) => item.checked)
      emit('select', selection)
    }

    const preview = (item) => {
      if (item.status !== '2') {
        ElMessage({
          message: '当前状态不可查看',
          type: 'warning',
        })
        return
      }
      ElMessage({
        message: '当前版本未开放，敬请期待',
        type: 'info',
      })
    }

    const download = async (item, e) => {
      if (item.status !== '2') {
        ElMessage({
          message: '当前状态不可下载',
          type: 'warning',
        })
        return
      }
      e.stopPropagation()
      try {
        const res = await paperDownload({
          fileUrls: [item.wordUrl].toString(),
        })
        if (res.response.data.status !== 200) {
          ElMessage({
            message: '文件下载失败，请稍后重试',
            type: 'error',
          })
        }
        const date = day().format('YYYYMMDD')
        const blobObj = new Blob([res.data], { type: 'application/zip' })
        const url = window.URL.createObjectURL(blobObj)
        const a = document.createElement('a')
        a.href = url
        a.download = `论文检测系统_${date}.zip` 
        document.body.appendChild(a)
        a.click()
        a.remove()
        window.URL.revokeObjectURL(url)
      }
      catch (e) {
        ElMessage({
          type: 'error',
          message: '下载失败，请稍后再试！',
        })
      }
    }
    
    const deleteTableItems = async (item) => {
      await deletePaperDetail([item.id])
      getTableList()
    }

    const selectionAll = ref(false)
    const changeSelectAll = (val) => {
      selectionAll.value = val
      tableData.value.forEach((item: any) => {
        item.checked = val
      })
      const selection = tableData.value.filter((item: any) => item.checked)
      emit('select', selection)
    }

    /** =============教师审核============== */
    const teacherModalVisible = ref(false)
    
    const currentTaskRow = ref()
    const handleSubmitDocumentToCheck = (item, e) => {
      /**
       * 检测状态 0.排队中 1.检测中 2.已完成 3.检测失败
       * 审核状态 0.未提交 1.审核中 2.不通过 3.初步审核通过 4.最终审核通过
       * 1. 排队中/检测中/检测失败
       *  或者 审核状态通过/不通过 
       *  - 提交按钮变灰色，点击提示“当前状态不可提交审核”
       * 2. 已完成 审核中 - 提示 “该检测报告正在审核中，请问是否重复提交？【确认】【取消】”
       * 3. 已完成 未提交 - 调接口检测是否有相同论文
       *   3.1 有相同论文 - 提示“同篇论文有正在审核的检测报告，请问是否重复提交？【确认】【取消】”
       *   3.2 无相同论文 - 继续提交审核
       */

      // 1. 排队中/检测中/检测失败 或者 审核状态通过/不通过 不可提交
      if (
        item.status === '0' 
        || item.status === '1'
        || item.status === '3'
        || item.approvalStatus === '2'
        || item.approvalStatus === '3'
        || item.approvalStatus === '4'
      ) {
        ElMessage({
          message: '当前状态不可提交审核',
          type: 'warning',
        })
      }

      // 2. 已完成 审核中 - 提示 “该检测报告正在审核中，请问是否重复提交？【确认】【取消】”
      else if (
        item.status === '2'
        && item.approvalStatus === '1'
      ) {
        ElMessageBox.confirm(
          '该检测报告正在审核中，请问是否重复提交？',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          },
        )
          .then(async () => {
            setTimeout(() => {
              currentTaskRow.value = item
              teacherModalVisible.value = true
            }, 200)

            return 0
          })
          .catch(() => {
            // eslint-disable-next-line no-console
            console.log('取消提交')
            return 0
          })
      }

      // 3. 已完成 未提交 - 调接口检测是否有相同论文
      // else if (
      //   item.status === '2'
      //   && item.approvalStatus === '0'
      // ) {
      // 暂时不做 TODO
      // const res = await checkSamePaper({
      //   taskId: item.id,
      // })
      // if (res.response.data.status === 200) {

      //   ElMessageBox.confirm(
      //     '同篇论文有正在审核的检测报告，请问是否重复提交？',
      //     {
      //       confirmButtonText: '确认',
      //       cancelButtonText: '取消',
      //       type: 'warning',
      //     },
      //   )
      //     .then(() => {
      //       // eslint-disable-next-line no-console
      //       console.log('继续提交')
      //     })
      //     .catch(() => {
      //       // eslint-disable-next-line no-console
      //       console.log('取消提交')
      //       return 0
      //     })
      // }
      // }

      // 4. 提交审核
      else {
        currentTaskRow.value = item
        teacherModalVisible.value = true
      }
    }

    /**
     * 提交给教师审核
     */
    const teacherNameTableRef = ref()

    async function handleSubmitToTeacherCheck() {
      const teacherName = teacherNameTableRef.value?.currentRow?.teacherName
      const teacherEmail = teacherNameTableRef.value?.currentRow?.teacherEmail

      if (!teacherName || !teacherEmail) {
        ElMessage({
          message: '请先选择教师',
          type: 'warning',
        })
        return
      }

      // ======= 提交审核时，显示 loading 效果 =======
      const teacherModalLoadingService = ElLoading.service({
        target: '.teacher-check-custom-modal',
        fullscreen: false,
      })

      try {
        const res = await sendEmailReviewApi({
          ...currentTaskRow.value,
          teacherEmail,
          teacherName,
        })

        if (res.response.data.status === 200) {
          ElMessage({
            message: res.response.data.data.msg,
            type: 'success',
          })
          getTableList()

          teacherModalVisible.value = false
        }
        else {
          ElMessage({
            message: '提交审核失败',
            type: 'error',
          })
        }
      }
      catch (error) {
        ElMessage({
          message: '提交审核失败',
          type: 'error',
        })
      }
      finally {
        // ======= 提交审核时，关闭 loading 效果 =======
        teacherModalLoadingService.close()
      }
    }
    /** =============教师审核============== */

    watch(() => props.viewType, () => {
      const selectIds = props.tableSelection?.map(item => (item as any).id)
      tableData.value.forEach((item: any, index) => {
        item.checked = selectIds?.includes(item.id) 
      })
    }, {
      immediate: true,
    })

    watch(() => props.tableSelection?.length, (len) => {
      if (len !== tableData.value.length) {
        selectionAll.value = false
      }
      else if (len === tableData.value.length) {
        selectionAll.value = true
      }
    })
    
    return () => {
      return (
        <div class="w-full mt-[-20px]">
          {
            tableData.value.length 
              ? (
                  <div>
                    <div class="flex w-full flex-nowrap top-[75px] items-center text-[14px] text-[rgba(0,0,0,0.85)] mb-[12px]">
                      <el-checkbox model-value={selectionAll.value} onChange={changeSelectAll} />
                      <span class="ml-[8px]">全选</span>
                      <span class="ml-[24px]">
                        已选择
                        <span class="text-[#06b190]">{props.tableSelection?.length}</span>
                        项
                      </span>
                    </div>
                    <div class="box-border w-full pr-[10px] pb-[50px] grid grid-cols-4 gap-[10px] h-[80vh] h-m-[500px] scrollbar-thin overflow-auto overflow-x-hidden">
                      {
                        tableData.value.map((item: any) => {
                          return (
                            <div class={`${cardContainerCSS} relative cursor-pointer w-[260px] h-[320px] bg-[#fff] box-border overflow-hidden rounded-[8px] shadow-sm flex flex-wrap justify-center p-[18px]`}>
                              <div class={`card-check-container absolute top-[10px] right-[10px] flex justify-end items-start ${props.tableSelection && props.tableSelection?.length ? 'opacity-1' : 'opacity-0'} z-[9] w-[260px] h-[320px]`} onClick={e => select(item, e)}>
                                <el-checkbox model-value={(item as any).checked} value={item.id} />
                              </div>
                              {/* header */}
                              <div class="box-border h-[44px] flex flex-nowrap items-start justify-start w-full">
                                <FileType class="w-[24px] mr-[12px]" type={item.titleCn.split('.')[item.titleCn.split('.').length - 1]} />
                                <div class="w-[180px] text-[14px] text-[rgba(0,0,0,0.85)] font-[500]">
                                  {item.taskName}
                                </div>
                              </div>
        
                              {/* content */}
                              <div class="flex flex-wrap justify-center h-[188px] content-between scale-[0.7]">
                                <div class="text-[14px] font-[600] mb-[12px]">硕士学术论文</div>
                                <div class="w-full scale-[0.8] box-border px-[30px] text-center font-[600] whitespace-nowrap underline overflow-hidden text-ellipsis">面向容器编排平台的自动化调优系统研究</div>
                                <div class="w-full scale-[0.86] mt-[20px]">
                                  <div class="w-full flex justify-start mb-[5px]">
                                    <span>作者姓名：</span>
                                    <span class="w-[70%] flex justify-center" style="border-bottom:1px solid rgba(0,0,0,0.85)">{item.author}</span>
                                  </div>
                                  <div class="w-full flex justify-star flex-wrap mb-[5px]">
                                    <span>指导教师：</span>
                                    <span class="flex flex-wrap w-[70%]">
                                      <span class="w-[100%] flex justify-center" style="border-bottom:1px solid rgba(0,0,0,0.85)">王新员</span>
                                      <span class="w-[100%] flex justify-center" style="border-bottom:1px solid rgba(0,0,0,0.85)">中国科学院软件研究所</span>
                                    </span>
                                  </div>            
                                  <div class="w-full flex justify-start mb-[5px]">
                                    <span>学位类别：</span>
                                    <span class="w-[70%] flex justify-center" style="border-bottom:1px solid rgba(0,0,0,0.85)">电子信息硕士</span>
                                  </div>            
                                  <div class="w-full flex justify-start mb-[5px]">
                                    <span>学科专业：</span>
                                    <span class="w-[70%] flex justify-center" style="border-bottom:1px solid rgba(0,0,0,0.85)">软件工程</span>
                                  </div>            
                                  <div class="w-full flex justify-start mb-[5px]">
                                    <span>培养单位：</span>
                                    <span class="w-[70%] flex justify-center" style="border-bottom:1px solid rgba(0,0,0,0.85)">中国科学院软件研究所</span>
                                  </div>
                                </div>
                              </div>
        
                              {/* footer */}
                              <div class={`w-full flex item-end status-flag ${props.tableSelection && props.tableSelection?.length ? 'opacity-0' : 'opacity-1'}`}>
                                <div class="w-full flex flex-nowrap justify-between items-end">
                                  <div class="text-[12px] text-[rgba(0,0,0,0.85)]">
                                    <span class="inline-block rounded-[50%] mr-[8px] w-[8px] h-[8px]" style={{ backgroundColor: statustMap[item.status].color }} />
                                    {statustMap[item.status].text} 
                                  </div>
                                  <div class="text-[12px] text-[rgba(0,0,0,0.45)]">2-2-12-02 09:54:34</div>
                                </div>
                              </div>
                              <div class={`flex flex-nowrap w-[100%] justify-between bottom-[15px] absolute z-[10] px-[20px] operation-bar ${props.tableSelection && props.tableSelection?.length ? 'opacity-1' : 'opacity-0'}`}>
                                {/* <el-tooltip
                                  content="提交审核"
                                  placement="top"
                                  show-after={1000}
                                  class="pointer-events-none"
                                >
                                  <div
                                    aria-label="提交给教师审核按钮"
                                    onClick={e => handleSubmitDocumentToCheck(item, e)}
                                    class={
                                      cn(
                                        'w-[24px] h-[24px]',
                                        'flex-shrink-0 flex justify-center items-center ',
                                        'rounded-[8px]',
                                        // 提交状态
                                        item.status === '0' && 'text-[#e9e9e9]', // 未提交
                                        item.status === '1' && 'text-[#e9e9e9] ', // 审核中
                                        item.status === '2' && 'text-currentColor ', // 检测完成
                                        item.status === '3' && 'text-[#e9e9e9] ', // 检测失败

                                        // 审核状态 0.未提交 1.审核中 2.不通过 3.初步审核通过 4.最终审核通过
                                        item.approvalStatus === '0' && 'text-currentColor ', // 未提交审核
                                        item.approvalStatus === '1' && 'text-currentColor ', // 审核中
                                        item.approvalStatus === '2' && 'text-[#e9e9e9] ', // 不通过
                                        item.approvalStatus === '3' && 'text-[#e9e9e9] ', // 审核通过
                                        item.approvalStatus === '4' && 'text-[#e9e9e9] ', // 审核通过

                                        // hover 状态
                                        item.status === '2' 
                                        && (item.approvalStatus === '0' || item.approvalStatus === '1') 
                                        && 'hover:bg-bg-active hover:text-active', // 检测完成 未提交审核 审核中 的时候， hover 有状态
                                      )
                                    }
                                  >
                                    <svg class="w-4 h-4 cursor-pointer" viewBox="0 0 16 16">
                                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <g transform="translate(-1198.000000, -236.000000)">
                                          <g transform="translate(156.000000, 168.000000)">
                                            <g transform="translate(16.000000, 62.000000)">
                                              <g transform="translate(1026.000000, 6.000000)">
                                                <rect x="0" y="0" width="16" height="16"></rect>
                                                <rect x="0" y="0" width="16" height="16"></rect>
                                                <path fill="currentColor" d="M10.5,7.5 C12.4329966,7.5 14,9.06700338 14,11 C14,11.7653622 13.7543359,12.4733464 13.3375588,13.0494016 L14.8774867,14.6537907 C15.0686928,14.8530264 15.0621836,15.1695419 14.862948,15.360748 C14.6858496,15.530709 14.4160856,15.5444505 14.2240343,15.4054763 L14.1559907,15.3462093 L12.6412616,13.7687843 C12.0493945,14.2271785 11.3065577,14.5 10.5,14.5 C8.56700338,14.5 7,12.9329966 7,11 C7,9.06700338 8.56700338,7.5 10.5,7.5 Z M10.5,8.5 C9.11928813,8.5 8,9.61928813 8,11 C8,12.3807119 9.11928813,13.5 10.5,13.5 C11.8807119,13.5 13,12.3807119 13,11 C13,9.61928813 11.8807119,8.5 10.5,8.5 Z M10.5,0.5 C11.8254834,0.5 12.9100387,1.53153594 12.9946823,2.83562431 L13,3 L13,6.74489842 C13,7.02104079 12.7761424,7.24489842 12.5,7.24489842 C12.2545401,7.24489842 12.0503916,7.06802326 12.0080557,6.83477405 L12,6.74489842 L12,3 C12,2.22030388 11.4051119,1.57955132 10.64446,1.50686658 L10.5,1.5 L3.5,1.5 C2.72030388,1.5 2.07955132,2.09488808 2.00686658,2.85553999 L2,3 L2,10 C2,10.7796961 2.59488808,11.4204487 3.35553999,11.4931334 L3.5,11.5 L5.70940931,11.5 C5.98555168,11.5 6.20940931,11.7238576 6.20940931,12 C6.20940931,12.2454599 6.03253415,12.4496084 5.79928494,12.4919443 L5.70940931,12.5 L3.5,12.5 C2.1745166,12.5 1.08996133,11.4684641 1.00531768,10.1643757 L1,10 L1,3 C1,1.6745166 2.03153594,0.589961328 3.33562431,0.50531768 L3.5,0.5 L10.5,0.5 Z M4.78137526,8 C5.05751763,8 5.28137526,8.22385763 5.28137526,8.5 C5.28137526,8.74545989 5.1045001,8.94960837 4.87125089,8.99194433 L4.78137526,9 L3.78137526,9 C3.50523289,9 3.28137526,8.77614237 3.28137526,8.5 C3.28137526,8.25454011 3.45825042,8.05039163 3.69149963,8.00805567 L3.78137526,8 L4.78137526,8 Z M5.78137526,6 C6.05751763,6 6.28137526,6.22385763 6.28137526,6.5 C6.28137526,6.74545989 6.1045001,6.94960837 5.87125089,6.99194433 L5.78137526,7 L3.78137526,7 C3.50523289,7 3.28137526,6.77614237 3.28137526,6.5 C3.28137526,6.25454011 3.45825042,6.05039163 3.69149963,6.00805567 L3.78137526,6 L5.78137526,6 Z M9.78137526,4.0376182 C10.0575176,4.0376182 10.2813753,4.26147583 10.2813753,4.5376182 C10.2813753,4.78307809 10.1045001,4.98722657 9.87125089,5.02956253 L9.78137526,5.0376182 L3.78137526,5.0376182 C3.50523289,5.0376182 3.28137526,4.81376057 3.28137526,4.5376182 C3.28137526,4.29215831 3.45825042,4.08800983 3.69149963,4.04567387 L3.78137526,4.0376182 L9.78137526,4.0376182 Z"></path>
                                              </g>
                                            </g>
                                          </g>
                                        </g>
                                      </g>
                                    </svg>
                                  </div>
                                </el-tooltip> */}
                                <div class="p-[5px] flex justify-center items-center rounded-[8px]">
                                  <el-tooltip
                                    content="查看报告"
                                    placement="top"
                                    show-after={1000}
                                    class="pointer-events-none"
                                  >
                                    <View class="w-[14px] h-[14px] cursor-pointer" style="color:#e9e9e9" onClick={() => preview(item)} />
                                  </el-tooltip>
                                </div>
                                <div class="p-[5px] flex justify-center items-center rounded-[8px] hover:bg-bg-active">
                                  <el-tooltip
                                    content="下载报告"
                                    placement="top"
                                    show-after={1000}
                                    class="pointer-events-none"
                                  >
                                    <Download class="w-[14px] h-[14px] cursor-pointer hover:text-active" style={{ color: item.status !== '2' ? '#e9e9e9' : '' }} onClick={e => download(item, e)} />
                                  </el-tooltip>
                                </div>

                                <el-popconfirm
                                  title="确认删除?"
                                  onConfirm={() => deleteTableItems(item)}
                                  v-slots={
                                    { reference: () => (
                                      <el-tooltip
                                        content="删除"
                                        placement="top"
                                        show-after={1000}
                                        class="pointer-events-none"
                                      >
                                        <div class="p-[5px] flex justify-center items-center rounded-[8px] hover:bg-bg-active">
                                          <Delete class="w-[14px] h-[14px] cursor-pointer hover:text-active" /> 
                                        </div>
                                      </el-tooltip>
                                    ) }
                                  }
                                >
                                </el-popconfirm>
                             
                              </div>
                            </div>
                          )
                        })
                      }
                    </div>
                  </div>
                ) : (
                  <div class="w-full h-[600px] flex justify-center items-center bg-[#fff] rounded-[8px] shadow-sm">
                    <img src={Empty as any} class="w-[50%]" />
                  </div>
                )
          }
          {/* 提交给教师审核的 Modal */}
          <Modal
            v-model:dialog-visible={teacherModalVisible.value}
            width={400}
            class="teacher-check-custom-modal"
            title="提交审核"
            v-slots={{
              footer: () => (
                <>
                  <el-button 
                    type="primary" 
                    class={
                      cn(
                        'hover:opacity-80 !border-0 bg-gradient-to-r from-[#6ED86E] to-[#1DB989]',
                        'w-[60px] h-[32px]',
                      )
                    }
                    onClick={() => handleSubmitToTeacherCheck()}
                  >
                    确认
                  </el-button>
          
                  <el-button 
                    class=" w-[60px] h-[32px] "
                    onClick={() => teacherModalVisible.value = false}
                  >
                    取消
                  </el-button>
                </>
              ),
            }}
          >
            <TeacherNameTable 
              ref={teacherNameTableRef}
            />
          </Modal>
        </div>
      ) 
    }
  },
})
