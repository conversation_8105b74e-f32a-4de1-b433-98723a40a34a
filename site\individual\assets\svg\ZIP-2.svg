<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/40文件/tex</title>
    <defs>
        <linearGradient x1="16.9921875%" y1="0%" x2="83.0078125%" y2="100%" id="linearGradient-1">
            <stop stop-color="#9EEDDC" offset="0%"></stop>
            <stop stop-color="#50BD5C" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-2" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-3"></use>
        </pattern>
        <image id="image-3" width="32" height="40" xlink:href="data:image/png;base64,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"></image>
        <pattern id="pattern-4" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-5"></use>
        </pattern>
        <image id="image-5" width="28" height="28" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHKADAAQAAAABAAAAHAAAAABkvfSiAAACzElEQVRIDY1WbVLsMAxry97/kNyAH28YGLZ9lmTFTls+sruxrdhSnJQp6+vH27HkOJbhLgddI8LPmOIlqrze6m+4ILNZDHaNz3kIueKVVyLGvAHE3iT9mCZBgJDlTPOTENNGH4fbFcz5LAbwIphyLFhT73dZpnNyd3diOPyLIKpmAUdx4HQdl4g9H+53YsjbvCMX2fo+/9JlHeeQJI0izKESBj92CFEtWk7Woueesdq7YJwMFhdfzoN8OtJ7WYi6y7MweCxQm0A/uYVwamNqakMBQCXBaoE1Y4Ko+oX4+RZZQwLXhjVh8jnMhybCSZgUTc7uWco4rGsspthCyjyWRwfWKMpvtAFyrGZn6GvFOrJUxdNBhM3Sgtar8DGwLg/zQ6QChKuLlSSBZ1OQxadydlKXWBeaRcSueerQsu6Nce4OBk1vMcHHriG2c2NEpk7MhZU+HjdZ2QXSJB2nGOPgiaLLl/Vl+dg/l+fxlHCrQOZP46bDnq6dg887RVcQ+vf1HsIbjxkb45566Tc+79BklxwKaRWz7mtfvvYnxZ7HTineLhXvhLUVb6g6HKr98tFZCuZd4c4wKBKz1gPTuesWMkN5WU8MTymJMmqmhAAiig9yUxhPEPuBVn7YBfnhxZ2HkZz6w1wdMiWmTFEjoBImIZEnGIIaysk1oiEWS0d2veLvF3H84g6dCC9GoF4eNjGIepAQtPhyfWR3yUkYtdFhkrAIkAvlQ0M5mQc4B0Rr+D6LAUduNuWu+Xo6kTrJxwrSEi6J4eGyYvBOBzhv1JzxtkCGwvKip8SdaJ5xIgCok2IygpwcttfDj7dFQfYk5pWyjWdy3Z/AVG4Z4PVG842P1YSlmnHigbkASI35EPNkL0frfHDE/zQYOTcxu7YuquyOWNj2ep/OriMdzL2XAAeeJefYTLbtRJvr1XpKhcxsjrwFx6P61qku86masv4DgxHB3lRWkPwAAAAASUVORK5CYII="></image>
        <path d="M34.2,18.6 L36,18.6 L36,20.4 L34.2,20.4 L34.2,18.6 Z M36,20.4 L37.8,20.4 L37.8,22.2 L36,22.2 L36,20.4 Z M34.2,22.2 L36,22.2 L36,24 L34.2,24 L34.2,22.2 Z M36,24 L37.8,24 L37.8,25.8 L36,25.8 L36,24 Z M34.2,25.8 L36,25.8 L36,27.6 L34.2,27.6 L34.2,25.8 Z M36,27.6 L37.8,27.6 L37.8,29.4 L36,29.4 L36,27.6 Z M34.2,29.4 L36,29.4 L36,31.2 L34.2,31.2 L34.2,29.4 Z M36,31.2 L37.8,31.2 L37.8,33 L36,33 L36,31.2 Z M37.3764365,34.8 C37.620856,34.8 37.8294511,34.9767065 37.8696334,35.2178005 L38.4089003,38.4534015 C38.5450928,39.270557 37.9930633,40.0433983 37.1759078,40.1795909 C37.0944107,40.1931737 37.0119307,40.2 36.9293094,40.2 L35.0706906,40.2 C34.2422635,40.2 33.5706906,39.5284271 33.5706906,38.7 C33.5706906,38.6173787 33.5775169,38.5348987 33.5910997,38.4534015 L34.1303666,35.2178005 C34.1705489,34.9767065 34.379144,34.8 34.6235635,34.8 L37.3764365,34.8 Z M36,37.2 C35.1715729,37.2 34.5,37.7372583 34.5,38.4 C34.5,39.0627417 35.1715729,39.6 36,39.6 C36.8284271,39.6 37.5,39.0627417 37.5,38.4 C37.5,37.7372583 36.8284271,37.2 36,37.2 Z" id="path-6"></path>
        <filter x="-147.3%" y="-23.1%" width="394.7%" height="164.8%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.347248793   0 0 0 0 0.836022418   0 0 0 0 0.486898401  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="-5.55111512e-15%" y1="100%" x2="50%" y2="50%" id="linearGradient-8">
            <stop stop-color="#ADEFD1" offset="0%"></stop>
            <stop stop-color="#ACEFD0" offset="100%"></stop>
        </linearGradient>
        <path d="M24,2.4 L36,14.4 L26.5,14.4 C25.1192881,14.4 24,13.2807119 24,11.9 L24,2.4 L24,2.4 Z" id="path-9"></path>
        <filter x="-45.8%" y="-29.2%" width="191.7%" height="191.7%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.260515118   0 0 0 0 0.659391984   0 0 0 0 0.418614531  0 0 0 0.496011801 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="论文检测0724" transform="translate(-260.000000, -547.000000)">
            <g id="icon/40文件/tex" transform="translate(260.000000, 547.000000)">
                <rect id="_mockplus_fix_" x="0" y="0" width="48" height="48"></rect>
                <rect id="矩形" opacity="0.05" x="0" y="0" width="48" height="48"></rect>
                <path d="M23.8446699,2.775 L35.625,14.5553301 L35.625,35.8 C35.625,37.0771585 35.1073292,38.2334085 34.2703689,39.0703689 C33.4334085,39.9073292 32.2771585,40.425 31,40.425 L31,40.425 L9.8,40.425 C8.52284152,40.425 7.36659152,39.9073292 6.52963114,39.0703689 C5.69267076,38.2334085 5.175,37.0771585 5.175,35.8 L5.175,35.8 L5.175,7.4 C5.175,6.12284152 5.69267076,4.96659152 6.52963114,4.12963114 C7.36659152,3.29267076 8.52284152,2.775 9.8,2.775 L9.8,2.775 L23.8446699,2.775 Z" id="矩形" stroke="url(#pattern-2)" stroke-width="0.75" fill="url(#linearGradient-1)"></path>
                <rect id="矩形" stroke="url(#pattern-4)" stroke-width="0.75" fill-opacity="0.1" fill="#90E7C3" x="15.375" y="18.975" width="26.85" height="26.85" rx="5"></rect>
                <g id="L" transform="translate(22.800000, 26.400000)" fill="#FFFFFF" fill-opacity="0.853201486" fill-rule="nonzero">
                    <polygon id="路径" points="0 0 0 10.8 7.2 10.8 7.2 9.39327731 1.55209581 9.39327731 1.55209581 0"></polygon>
                </g>
                <g id="形状结合">
                    <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-6"></use>
                </g>
                <g id="矩形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                    <use fill="url(#linearGradient-8)" fill-rule="evenodd" xlink:href="#path-9"></use>
                </g>
            </g>
        </g>
    </g>
</svg>