<script setup lang="ts"></script>

<template>
  <div class="container">
    <div class="wave" />
    <p>45%</p>
  </div>
</template>

<style scoped>
.container {
  width: 480px;
  height: 304px;
  border: 5px solid rgb(118, 218, 255);
  overflow: hidden;
}

.wave {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: rgb(118, 218, 255);
}

.wave::before,
.wave::after {
  content: '';
  position: absolute;
  width: 200%;
  aspect-ratio: 1;
  top: 0;
  left: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 45%;
  transform: translate(-50%, -70%) rotate(0);
  animation: rotate 6s linear infinite;
  z-index: 1;
}

.wave::after {
  border-radius: 47%;
  background-color: rgba(255, 255, 255, 0.9);
  transform: translate(-50%, -70%) rotate(0);
  animation: rotate 10s linear -5s infinite;
  z-index: 2;
}

p {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 36px;
  color: #000;
  z-index: 10;
}

@keyframes rotate {
  50% {
    transform: translate(-50%, -73%) rotate(180deg);
  }
  100% {
    transform: translate(-50%, -70%) rotate(360deg);
  }
}
</style>
