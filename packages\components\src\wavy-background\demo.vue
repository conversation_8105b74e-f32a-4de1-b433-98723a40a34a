<script setup lang="ts">
import { onMounted, ref } from 'vue'
import WavyBackground from './index.vue'
import PaperWavyBackground from './sample/PaperWavyBackground.vue'
// 动态进度示例
const dynamicProgress = ref(0)
const animatedProgress = ref(0)
const isAnimating = ref(false)

// 启动动态进度动画
function startDynamicAnimation() {
  if (isAnimating.value) 
    return

  isAnimating.value = true
  dynamicProgress.value = 0

  const interval = setInterval(() => {
    if (dynamicProgress.value < 100) {
      dynamicProgress.value += 1
    }
    else {
      clearInterval(interval)
      isAnimating.value = false
    }
  }, 50)
}

// 重置动态进度
function resetDynamicProgress() {
  dynamicProgress.value = 0
  isAnimating.value = false
}

// 启动平滑动画
function startSmoothAnimation() {
  animatedProgress.value = animatedProgress.value === 0 ? 85 : 0
}

// 组件挂载后启动一次演示
onMounted(() => {
  setTimeout(() => {
    startDynamicAnimation()
  }, 1000)
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">
        WavyBackground 波浪背景组件演示
      </h1>

      <!-- 业务组件用法 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          业务组件用法
        </h2>
        <PaperWavyBackground />
      </section>

      <!-- 基础用法 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          基础用法
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              默认样式 (0%)
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground>
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    默认波浪
                  </h4>
                  <p class="text-sm opacity-80">
                    进度: 0%
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              中等进度 (45%)
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground :progress="45">
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    中等进度
                  </h4>
                  <p class="text-sm opacity-80">
                    进度: 45%
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              高进度 (85%)
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground :progress="85">
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    高进度
                  </h4>
                  <p class="text-sm opacity-80">
                    进度: 85%
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>
        </div>
      </section>

      <!-- 自定义颜色 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          自定义颜色
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              蓝色系
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="60"
                :colors="['#3b82f6', '#1d4ed8', '#1e40af']"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    蓝色波浪
                  </h4>
                  <p class="text-sm opacity-80">
                    进度: 60%
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              绿色系
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="75"
                :colors="['#10b981', '#059669', '#047857']"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    绿色波浪
                  </h4>
                  <p class="text-sm opacity-80">
                    进度: 75%
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              紫色系
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="90"
                :colors="['#8b5cf6', '#7c3aed', '#6d28d9']"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    紫色波浪
                  </h4>
                  <p class="text-sm opacity-80">
                    进度: 90%
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>
        </div>
      </section>

      <!-- 动态进度 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          动态进度
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-700">
              快速递增动画
            </h3>
            <div class="flex gap-2 mb-4">
              <button
                :disabled="isAnimating"
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                @click="startDynamicAnimation"
              >
                {{ isAnimating ? "动画中..." : "开始动画" }}
              </button>
              <button
                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                @click="resetDynamicProgress"
              >
                重置
              </button>
              <span class="px-3 py-2 bg-gray-100 rounded text-gray-700">
                {{ dynamicProgress }}%
              </span>
            </div>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="dynamicProgress"
                :colors="['#f59e0b', '#d97706', '#b45309']"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    动态进度
                  </h4>
                  <p class="text-sm opacity-80">
                    进度: {{ dynamicProgress }}%
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-700">
              平滑过渡动画
            </h3>
            <div class="flex gap-2 mb-4">
              <button
                class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                @click="startSmoothAnimation"
              >
                切换进度
              </button>
              <span class="px-3 py-2 bg-gray-100 rounded text-gray-700">
                {{ animatedProgress }}%
              </span>
            </div>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="animatedProgress"
                :colors="['#ef4444', '#dc2626', '#b91c1c']"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    平滑过渡
                  </h4>
                  <p class="text-sm opacity-80">
                    进度: {{ animatedProgress }}%
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>
        </div>
      </section>

      <!-- 不同波浪数量 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          不同波浪数量
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              单层波浪
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground :progress="65" :colors="['#06b6d4']">
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    单层
                  </h4>
                  <p class="text-sm opacity-80">
                    1个波浪
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              双层波浪
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground :progress="65" :colors="['#8b5cf6', '#a855f7']">
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    双层
                  </h4>
                  <p class="text-sm opacity-80">
                    2个波浪
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              五层波浪
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="65"
                :colors="[
                  '#f59e0b',
                  '#f97316',
                  '#ef4444',
                  '#ec4899',
                  '#8b5cf6',
                ]"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    五层
                  </h4>
                  <p class="text-sm opacity-80">
                    5个波浪
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>
        </div>
      </section>

      <!-- 特殊进度值 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          特殊进度值
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              空状态 (0%)
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground :progress="0">
                <div class="text-gray-800 text-center">
                  <h4 class="text-xl font-semibold">
                    空状态
                  </h4>
                  <p class="text-sm opacity-80">
                    无波浪
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              少量 (5%)
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground :progress="5">
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    少量
                  </h4>
                  <p class="text-sm opacity-80">
                    底部波浪
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              接近满 (95%)
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground :progress="95">
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    接近满
                  </h4>
                  <p class="text-sm opacity-80">
                    顶部波浪
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              满状态 (100%)
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="100"
                :colors="['#10b981', '#059669', '#047857']"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    满状态
                  </h4>
                  <p class="text-sm opacity-80">
                    完全填充
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>
        </div>
      </section>

      <!-- 速度和效果 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          速度和效果
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              慢速波浪
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="50"
                speed="slow"
                :colors="['#06b6d4', '#0891b2', '#0e7490']"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    慢速
                  </h4>
                  <p class="text-sm opacity-80">
                    平缓波动
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>

          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-700">
              快速波浪
            </h3>
            <div
              class="h-48 relative border border-gray-200 rounded-lg overflow-hidden"
            >
              <WavyBackground
                :progress="50"
                speed="fast"
                :colors="['#f59e0b', '#d97706', '#b45309']"
              >
                <div class="text-white text-center">
                  <h4 class="text-xl font-semibold">
                    快速
                  </h4>
                  <p class="text-sm opacity-80">
                    活跃波动
                  </p>
                </div>
              </WavyBackground>
            </div>
          </div>
        </div>
      </section>

      <!-- API 文档 -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-800 mb-6">
          API 文档
        </h2>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4">
            Props
          </h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    属性名
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    类型
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    默认值
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    说明
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    progress
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    number
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    0
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    进度值，范围 0-100，控制波浪从底部到顶部的填充程度
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    colors
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    string[]
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ['#000000', '#c084fc', '#e87999']
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    波浪颜色数组，数组长度决定波浪层数
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    speed
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    'slow' | 'fast'
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    'fast'
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    波浪动画速度
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    waveOpacity
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    number
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    1
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    波浪透明度，范围 0-1
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    backgroundFill
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    string
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    'transparent'
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    背景填充颜色
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    blur
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    number
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    0
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    模糊效果强度
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    class
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    string
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    内容区域的 CSS 类名
                  </td>
                </tr>
                <tr>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    containerClass
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    string
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    -
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    容器的 CSS 类名
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <h3 class="text-lg font-medium text-gray-700 mb-4 mt-8">
            使用示例
          </h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <pre class="text-sm text-gray-800"><code>&lt;!-- 基础用法 --&gt;
&lt;WavyBackground&gt;
  &lt;div class="text-white text-center"&gt;
    &lt;h1&gt;内容区域&lt;/h1&gt;
  &lt;/div&gt;
&lt;/WavyBackground&gt;

&lt;!-- 自定义进度 --&gt;
&lt;WavyBackground :progress="75"&gt;
  &lt;div class="text-white text-center"&gt;
    &lt;h1&gt;75% 进度&lt;/h1&gt;
  &lt;/div&gt;
&lt;/WavyBackground&gt;

&lt;!-- 自定义颜色和波浪数量 --&gt;
&lt;WavyBackground
  :progress="60"
  :colors="['#3b82f6', '#1d4ed8', '#1e40af']"
&gt;
  &lt;div class="text-white text-center"&gt;
    &lt;h1&gt;蓝色三层波浪&lt;/h1&gt;
  &lt;/div&gt;
&lt;/WavyBackground&gt;

&lt;!-- 慢速波浪 --&gt;
&lt;WavyBackground
  :progress="50"
  speed="slow"
  :wave-opacity="0.8"
&gt;
  &lt;div class="text-white text-center"&gt;
    &lt;h1&gt;慢速波浪&lt;/h1&gt;
  &lt;/div&gt;
&lt;/WavyBackground&gt;</code></pre>
          </div>

          <h3 class="text-lg font-medium text-gray-700 mb-4 mt-8">
            特性说明
          </h3>
          <ul class="list-disc list-inside space-y-2 text-sm text-gray-600">
            <li>支持 0-100 的进度值，波浪从底部到顶部填充</li>
            <li>内置平滑过渡动画，进度变化时自动应用</li>
            <li>波浪数量根据 colors 数组长度自动确定</li>
            <li>支持自定义波浪颜色、速度、透明度等属性</li>
            <li>响应式设计，自动适应容器大小</li>
            <li>多层波浪效果，营造真实的液体感</li>
            <li>支持插槽内容，可在波浪上方显示任意内容</li>
            <li>兼容 Safari 浏览器的模糊效果</li>
          </ul>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
/* 为动画添加平滑过渡 */
.wavy-background {
  transition: all 0.3s ease-out;
}
</style>
