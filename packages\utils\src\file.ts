import type { FileTypeResult } from 'file-type'
import size from 'file-size'
import { fileTypeFromStream } from 'file-type'

export async function getFileType(fileStream: ReadableStream): ReturnType<typeof fileTypeFromStream> {
  const fileType = await fileTypeFromStream(fileStream) 
  return fileType as FileTypeResult
}

export function getFileSize(fileSize: number): number {
  return size(fileSize, { fixed: 2 }).to('MB')
}
