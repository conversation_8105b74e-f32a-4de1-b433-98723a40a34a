// @ts-ignore
import { registerGlobComp } from '@/components/registerGlobComp'
import { createApp } from 'vue'
import App from './App.vue' 
import { setupRouter } from './router/index'
import { setupStore } from './store/index'
// @ts-ignore
import '@/style/index.css'

export default async function initApp() {
  const app = createApp(App)
      
  registerGlobComp(app)
  
  setupStore(app) 

  setupRouter(app)
  
  app.mount('#app')

  return app
}

initApp()
