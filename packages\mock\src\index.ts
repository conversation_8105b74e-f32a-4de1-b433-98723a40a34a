import { serve } from '@hono/node-server'
import { Hono } from 'hono'

const app = new Hono().basePath('/api/review')

// 添加日志中间件
app.use('*', async (c, next) => {
  const start = Date.now()
  const method = c.req.method
  const url = c.req.url
  const userAgent = c.req.header('User-Agent') || 'Unknown'
  
  console.log(`[${new Date().toISOString()}] ${method} ${url}`)
  console.log(`User-Agent: ${userAgent}`)
  
  await next()
  
  const end = Date.now()
  console.log(`[${new Date().toISOString()}] ${method} ${url} - ${c.res.status} (${end - start}ms)`)
  console.log('---')
})

app.post('/getPaperReviewNotice', (c) => {
  console.log('c', c)
  return c.json({
    code: 200,
    message: 'success',
    data: null,    
  })
})

app.post('/uploadPaper', async (c) => {
  const formData = await c.req.formData()
  const file = formData.get('file') as File

  return c.json({
    code: 200,
    message: 'success',
    data: {
      taskId: '123456',
      fileName: file.name,
      fileUrl: `https://example.com/files/${file.name}`,
    },
  })
})

serve({
  fetch: app.fetch,
  port: 3000,
}, (info) => {
  console.log(`Server is running on http://localhost:${info.port}`)
})
