<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/40文件/WORD</title>
    <defs>
        <linearGradient x1="16.9921875%" y1="0%" x2="59.4225047%" y2="66.1108901%" id="linearGradient-1">
            <stop stop-color="#88BEFF" offset="0%"></stop>
            <stop stop-color="#4297FC" offset="100%"></stop>
        </linearGradient>
        <pattern id="pattern-2" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-3"></use>
        </pattern>
        <image id="image-3" width="32" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAoCAYAAACfKfiZAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAKAAAAACc9ikyAAAE90lEQVRYCZWYXY4bRwyERyN5EcMPeclVc7LcI7fwEYxgvSsp9VWzejgjyYBpzPCn2awiu0eGffr7n+v9tJyW+7Isd72e6RvxWntmO1b7X9nHuko31kphzJPeJ726ZgVJLLaDT14upXjIJuUVOAzWLHYS2RhC8X9Xb7XHztHs6Bxw1j2BmwycSDpGIxCxHmr65VrtiivS/T6R4ESvOdNxdvujoHKmcCRj1MNrB6q1nR/EQ3xMQItm6aRGQqgdOJM44O7crc6Y6jOfDQW1XBg/FwEdWU+Q4I+kSKQbYv3J8VHQRdkjCXBi1uXERl9INAk5DyTUMusd0JtBeCGs5yHFRGy0uBJS58LZA4CsekLCoHqteojZLz13y+8yuwZAD7Ut+DLslk2cnB0BEiCBANiPIgS8huNqeEMohqDyPPha8BovCepyU3u5XNTdiTLO3AclhIDcYTcS7lwbKWhbxoxhU7R0J0psToCkdE++hdkraBKCNYkGXFlTTVBFqBf/FTg5l6veANOZ/lLYPV5gQkrgLrCB3Jts8iOYgOU3xcDyEyfvSMYxvTwBFjkGintTtMDPiiMXfatrfRW+mJDQc1fOBFfeJCE7RNi/I1MBYpcrHVJMHhNwUQrX47mzQfLlPEj0L4d4SBtcvkpO8FfA7EPGHVCWgUszYrpMpy448pc3kThrgaNTyuyMRiAwJyA7Y5fpRGvMGLLnBKiUSQR4ktBaH+fbeRzNB4UlkAk4XxX1k4+NdNDEiPsS5kKZgIK+ZNL4IUGHV10SgIBgCiYq32vSgM8JkFVIpdhmclSIbBMgogTGioSMdRH5FMCHHvS3tzqKil2vA3wQHEAhMCoewIuV78ADrdphMnodSZ01if80/7++3ZcfH6flXXYH7vVm92Fx0JrASHmW6FgtoHyu0oz8Qx3/+31Z/vy6LH9c9hPrGCHfY932HTgCkRCwaHhyxoz/p8B//Bxdv3+y+6Tfie2+sJ+jsx5qbzdWvgMmoJSAMUJigBJDc9N9BwT+/jkm8EVfg9dECuFHK78lXGxISM0jZA3JpcceEyBLEgIBJZzv22Ov0TMB7oG7VNJNG07+8RiAWjIoeDwmEUP+FMU8AQIB711jp0O6z/j5/KiXmuTYJ7+qZ91aL+pjj1clKTb/LpBtsHQfcDTAuXizsKuNQiavud40Y7qHBNpyBAcoorV5B1xEiwZWQn5UOHvAIcFed6+NDd/lvF9W6vtE5OA7l1cWs1n+nACsH7pXMATmmJXXhZrbc5et6gXkX1TsgDdganCHTIDiE1wL6Z443UOCvb50MtB5KGShhgye0XI5bCQYLbOvrxT32KMFGN/du+LYRA2enbCuJ2nMwPYWsJ8jYi+2tV7bESg4pyDbJDgXyQQu9AcSI81AXqMWBkDSfPfddzprkrWPO52j6Z4c6njczZa5k6rl2OhUU6j90RQjDz82G+Y/zTo4thPJeCHutK1R1wgtFrMwJ3By0StTDmAnwebevZ3EWHwiARpdb1MIsQ4ce/ev4xCh9rFDkwnocTHxF9rEjFhDku2Y8ucRmHUtpI4/O5wARieh6wKY3brWmELSdimFtZtAEoxTYKhf4aZ4NDVS5yFWoMST4wk82zRBf4NIigZ4AG2oXt9cs/D/EXH2EeM9A22x5HbdSsxw/5K6nfbZ4wlkR2FsI08gCehnsb6O3bu0u9GLFf0/ZQq+ajd4dtoAAAAASUVORK5CYII="></image>
        <pattern id="pattern-4" width="100%" height="100%" patternUnits="objectBoundingBox">
            <use xlink:href="#image-5"></use>
        </pattern>
        <image id="image-5" width="28" height="28" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHKADAAQAAAABAAAAHAAAAABkvfSiAAACqUlEQVRIDY1WQZLbMAxLtvn/9/qJHntop9u4AEhQsJzsVDMRQRIkRFub7P37j+dxiyUHWwaP8Im5DjJexpWc+qZPww9Vx3Ynxia7x8NP6Ka7HU4neMiL4JBSMXDAoc44jFjVMMSY/og8fS033e0pCSdrjU+2HT3+al0TmtSx1ReKFn31mJMvPALwBhfgzvf/sRKX8hJrxRHeaN1X0cENPJkuWsfqkdLBp2Nby5jykll16/YW6STWdeyvS9N6I5rCmszj2bJBkqJhHbySeQjTHwTuoyA2+gc2x2UVrM4uptUHm2KymE3OmVsH6XdIQnCiuIq4W3xFgFjkWtkl1qnKmwP6mjCC051VXFCj4L1Vafj9xLQowiUmn3EDYC+GHnT23B2BibE7HdjWY4mWngxyTwDxsck637bI5TzyJG7I9+fFfLiaUgdic3wkRtwFsnYy1lgTTvMLqADrKaIpYb/hbv/6c7t94rn6wJuGO12s3uEp+qbySRJyBxQo9vP3IWFNjy2fwqnf5jz8LN7oSKC1NA0n+oQ6p/wLy4vE6fNCSWM7gd3rhGBzCi8hbLQM63YCsAFFglol7gyuIROsZ6Bu6aWqCWCRKDK2EmMlFor1XgHFwaYpRRZDnEYy5OOWLsYgAGOnaRnzh6f3Y2RQcW5cPAyMa81j+vpd2sWsc4GaKcBtLTbVGlDCc4BO67B9mLe/FiexPoSEnbBWTxOaa9qua6oG0O+hGvk0sO5ZApFw5Svbwpli/T7t6V8ME5p3Es5GO+Z0mvALUQvrHUogxkxhNUdAaY9uRQtIrUWdCzutATRh9mGSa7cV/Y/dh3hBZc/5n4b5EWkgg83x6dGBHkxhX33FvhBdj7S75bQjEOAiHrkd5oGcm79DBaKbIDaH8gvCxbZufLLtOGbuPwjBfeWhPijvAAAAAElFTkSuQmCC"></image>
        <linearGradient x1="-5.55111512e-15%" y1="100%" x2="24.7512398%" y2="8.27644558%" id="linearGradient-6">
            <stop stop-color="#91C3FF" offset="0%"></stop>
            <stop stop-color="#80BAFF" offset="100%"></stop>
        </linearGradient>
        <path d="M24,2.4 L36,14.4 L26.5,14.4 C25.1192881,14.4 24,13.2807119 24,11.9 L24,2.4 L24,2.4 Z" id="path-7"></path>
        <filter x="-45.8%" y="-29.2%" width="191.7%" height="191.7%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.455048906   0 0 0 0 0.995754076  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-9" points="33.1818 37.7994983 30.4176 37.7994983 28.8001332 31.3281592 27.1818 37.7994983 24.4176 37.7994983 21.7164 26.9994983 23.8806 26.9994983 25.7995332 34.6719592 27.7176 26.9994983 29.8818 26.9994983 31.8001332 34.6707592 33.7176 26.9994983 35.8818 26.9994983"></polygon>
        <filter x="-49.4%" y="-46.3%" width="198.8%" height="229.6%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.136208592   0 0 0 0 0.481175748   0 0 0 0 0.891077899  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="论文检测hover0724" transform="translate(-204.000000, -547.000000)">
            <g id="编组-3" transform="translate(140.000000, 281.000000)">
                <g id="编组-5" transform="translate(64.000000, 86.000000)">
                    <g id="icon/40文件/WORD" transform="translate(0.000000, 180.000000)">
                        <rect id="_mockplus_fix_" x="0" y="0" width="48" height="48"></rect>
                        <rect id="矩形" opacity="0.05" x="0" y="0" width="48" height="48"></rect>
                        <path d="M23.8446699,2.775 L35.625,14.5553301 L35.625,35.8 C35.625,37.0771585 35.1073292,38.2334085 34.2703689,39.0703689 C33.4334085,39.9073292 32.2771585,40.425 31,40.425 L31,40.425 L9.8,40.425 C8.52284152,40.425 7.36659152,39.9073292 6.52963114,39.0703689 C5.69267076,38.2334085 5.175,37.0771585 5.175,35.8 L5.175,35.8 L5.175,7.4 C5.175,6.12284152 5.69267076,4.96659152 6.52963114,4.12963114 C7.36659152,3.29267076 8.52284152,2.775 9.8,2.775 L9.8,2.775 L23.8446699,2.775 Z" id="矩形" stroke="url(#pattern-2)" stroke-width="0.75" fill="url(#linearGradient-1)"></path>
                        <rect id="矩形" stroke="url(#pattern-4)" stroke-width="0.75" fill-opacity="0.1" fill="#60A9FD" x="15.375" y="18.975" width="26.85" height="26.85" rx="5"></rect>
                        <g id="矩形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                            <use fill="url(#linearGradient-6)" fill-rule="evenodd" xlink:href="#path-7"></use>
                        </g>
                        <g id="路径" fill-rule="nonzero">
                            <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                            <use fill="#FFFFFF" xlink:href="#path-9"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>