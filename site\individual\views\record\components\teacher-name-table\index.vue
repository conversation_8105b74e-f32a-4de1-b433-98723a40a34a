<script lang="ts" setup>
import type { TableColumnCtx } from 'element-plus/es/components/table'
import { Pagination } from '@paper-review/components'
import { getTeacherInfoApi } from '../../../../service/api/sys'

/** 当前选中的行 */
const currentRow = ref()
/** 检索查询 */
const currentSearchText = ref('')

/** 定义表格列 */
const tableColumn: Partial<TableColumnCtx<any>>[] = [
  { 
    label: '导师姓名', 
    prop: 'teacherName', 
    // width: 80,
  },
]

/** 获取数据 */
const { 
  totalCount, 
  tableData,
  pageInfo, 
  tableLoading,
  getTableList,
} = useTableList(() => {
  return new Promise((resolve) => {
    resolve(getTeacherInfoApi({ keyWord: currentSearchText.value }))
  })
})

/** 监听检索查询 */
watch(currentSearchText, () => {
  handleSearch()
})

/** 检索查询 */
function handleSearch() {
  getTableList({ keyWord: currentSearchText.value })
}

/**
 * 行点击事件
 * @param row 行数据
 */
function handleRowClick(row) {
  if (currentRow.value === row) {
    currentRow.value = null
  }
  else {
    currentRow.value = row
  }
}

/**
 * 选中的行样式
 * @param row 行数据
 */
function tableRowClassName({ row }: { row: any }) {
  if (row === currentRow.value) {
    return '!bg-[#EAF9F9]'
  }
  return ''
}

/**
 * 暴露当前行
 */
defineExpose({
  currentRow,
})
</script>

<template>
  <div class="flex flex-col gap-[17px]">
    <!-- <div class="w-full">
      <el-input 
        v-model="currentSearchText" 
        :suffix-icon="Search" 
        autocomplete="off" 
        class="h-[32px]" 
        placeholder="请输入导师姓名"
        @keyup.enter="handleSearch"
      />
    </div> -->

    <el-table 
      v-loading="tableLoading"
      :show-header="true"
      :data="tableData" 
      :row-class-name="tableRowClassName"
      style="
        width: 100%;
        min-height: 240px;
        --el-table-row-hover-bg-color: #EAF9F960;
      "
      row-style="cursor: pointer;"
      @row-click="(row) => handleRowClick(row)"
    >
      <el-table-column 
        v-for="(item, index) in tableColumn" 
        :key="index"
        v-bind="item"
      />

      <el-table-column width="55" text-align="center">
        <template #default="scope">
          <div
            v-if="currentRow === scope.row" 
            class="w-full h-full flex justify-center items-center" 
          >
            <svg class="w-4 h-4 text-[#06B190]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path fill="currentColor" d="m13 24l-9-9l1.414-1.414L13 21.171L26.586 7.586L28 9z" /></svg>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <Pagination 
      :hide-on-single-page="true"
      size="small"
      :pager-count="2"
      background
      :total="totalCount" 
      :page-size="pageInfo.pageSize" 
      :current-page="pageInfo.pageNum" 
      @handle-current-change="(val) => pageInfo.pageNum = val" 
      @handle-size-change="(val) => pageInfo.pageSize = val"
    />
  </div>
</template>

<style>

</style>
