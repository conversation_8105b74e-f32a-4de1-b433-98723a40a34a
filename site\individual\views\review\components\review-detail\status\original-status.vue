<script setup lang="ts">
import { getFileSize, getFileType } from '@paper-review/utils'
import { ElMessage } from 'element-plus'
import { uploadPaper, uploadPdf } from '../../../../../service/api/sys'
import { getToken } from '../../../../../store/modules/user'
import LeftInstruct from './left-instruct.vue'
import UPLOAD from './uploadFile.vue'

const { setStatus } = inject('useStatus') as any
const STATUS = inject('STATUS') as any
const { setFrontTempFileInfo } = inject('useFrontTempFileInfo') as any
const dragOver = ref(false)
const uploadFileRef = useTemplateRef<any>('uploadFileRef')
const loading = ref(false)
const uploaded = ref(false)
const router = useRouter()
const currentFile = ref<any>(null)
function dragover(e: DragEvent) {
  e.stopPropagation()
  e.preventDefault()
  if (dragOver.value) 
    return
  dragOver.value = true 
}
function dragleave(e: DragEvent) {
  e.stopPropagation()
  e.preventDefault()
  dragOver.value = false
}
function onExceed() {
  ElMessage({
    message: '上传失败，仅允许上传一个文件',
    type: 'warning',
    plain: true,
  })
  uploadFileRef.value?.clearFiles()
  dragOver.value = false
}
function clearUploadState() {
  uploaded.value = false
  dragOver.value = false
  uploadFileRef.value?.clearFiles()
  currentFile.value = null
}
async function onBeforeUpload(file: File, type?: string) {
  const defaultTypes = type ? [type] : ['docx', 'zip']
  !type && (currentFile.value = file)
  loading.value = true
  await nextTick()
  try {
    const checkFileType = await getFileType(file.stream())
    const checkSuffix = file.name.split('.').pop() || ''
    const checkFileTypeInValid = !checkFileType || !defaultTypes.includes(checkFileType?.ext)
    const checkSuffixInValid = !defaultTypes.includes(checkSuffix)
    if (checkFileTypeInValid || checkSuffixInValid) {
      ElMessage({
        message: '上传失败，文件类型不符合要求',
        type: 'warning',
        plain: true,
      })
      loading.value = false
      clearUploadState()
      return false
    }
    // @ts-ignore
    file.fileType = checkFileType
    if (getFileSize(file.size) > 500) {
      ElMessage({
        message: '上传失败，文件大小不得超出500MB',
        type: 'warning',
        plain: true,
      })
      loading.value = false
      clearUploadState()
      return false
    }
    // loading.value = false
  }
  catch (e) {
    loading.value = false
  }
  finally {
    dragOver.value = false
  }
}
let eventSource: EventSource | null = null 

const uploadCheckInfo = ref<any>()
async function sseConnect(taskId, options, STATUS, url = '/api/review/getCoverResult') {
  if (!eventSource) {
    eventSource = new EventSource(`${url}?taskId=${taskId}`)
    eventSource.onmessage = async function (event) {
      const data = JSON.parse(event.data)
      const { zipStatus, zipErrorMessage, taskId, coverResult, coverStatus } = data

      Object.keys(data).forEach((key) => {
        options.file[key] = data[key]
      })
      options.file.reviewId = taskId
      uploadCheckInfo.value = data
      // setFrontTempFileInfo(options.file)
      await nextTick()
      // coverResult: "{\"degree\": \"硕士毕业论文\", \"titleCn\": null, \"author\": \"朱自然\", \"school\": \"中科院软件所\"}"
      if (Number.parseInt(zipStatus) === 3 && !zipErrorMessage && coverResult && Number.parseInt(coverStatus) === 2) {
        const transferCoverResult = Object.assign({}, options.file, JSON.parse(coverResult)) 
        setFrontTempFileInfo(transferCoverResult)
        setStatus(STATUS.TEMPLATSTATUS)
        if (data) {
          useTimeoutFn(() => {
            eventSource?.close()
            loading.value = false
          }, 1000)
        }
      }
    }
  }
}

async function uploadFile(options: any, receiveTaskId?: any) {
  /** loading结束控制在sseConnect方法中 */
  loading.value = true
  const formData = new FormData()
  formData.append('file', options.file)
  receiveTaskId && formData.append('taskId', receiveTaskId)
  !receiveTaskId && (currentFile.value = options.file)
  if (!getToken()) { 
    return router.push({ name: 'login', query: { type: 'login' } })
  }
  else {
    try {
      const uploadApi = receiveTaskId ? uploadPdf : uploadPaper
      const res = await uploadApi(formData)
      if (res.data.code !== 200) {
        ElMessage({
          message: res.data.msg,
          type: 'warning',
        })
        clearUploadState()
        return
      }
      if (!res.data.data) 
        return clearUploadState()
      const { taskId, fileName, fileUrl, id } = res.data.data
      options.file.fileName = fileName
      options.file.fileUrl = fileUrl
      options.file.id = id
      if (!receiveTaskId)
        sseConnect(taskId, options, STATUS, '/api/review/getZipResult')
    }
    catch (e) {
      loading.value = false
      clearUploadState()
      ElMessage({
        message: '文件上传失败，请稍后重试',
        type: 'error',
      })
    }
    finally {
      uploaded.value = true
      loading.value = false
    }
  }
}
</script>

<template>
  <div class="w-full h-full flex flex-nowrap items-center box-border p-[50px] justify-between" style="background: linear-gradient(90deg,#D2FCD7,#BCFAF0);">
    <LeftInstruct 
      :current-file="currentFile"
      :loading="loading" 
      :uploaded="uploaded" 
      :drag-over="dragOver" 
      :upload-check-info="uploadCheckInfo"
    />
    <UPLOAD 
      ref="uploadFileRef"
      :current-file="currentFile"
      :drag-over="dragOver"
      :loading="loading" 
      :uploaded="uploaded"
      :upload-check-info="uploadCheckInfo" 
      @on-exceed="onExceed"
      @on-before-upload="onBeforeUpload"
      @upload-file="uploadFile"
      @dragover="dragover" 
      @dragleave="dragleave"
    />
  </div>
</template>

<style scoped>
:deep(.el-upload-dragger) {
  height: 320px;
}
.bg-line {
  margin-left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  color: #fff;
  width: 128px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(90deg, #82df67, #06b190);
}
.select-text {
  display: none;
}
</style>