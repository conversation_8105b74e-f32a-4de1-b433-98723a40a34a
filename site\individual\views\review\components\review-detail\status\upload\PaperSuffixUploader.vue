<script setup lang="ts">
import { motion } from 'motion-v'

import errorIcon from './assets/error.png'

import wordIcon from './assets/word.png'
import wordBg from './assets/wordBg.png'
import zipIcon from './assets/zip.png'
import zipBg from './assets/zipBg.png'
import FileSuffixUploader from './index.vue'

const props = defineProps<{
  onExceed: () => void
}>()

const emits = defineEmits([
  'handleFileSelected',
  'handleFileRejected',
  'dragEnter',
  'dragLeave',
])
const message = ref('')
const messageType = ref<'success' | 'error'>('success')
const isHoveredDefaultUploader = ref(false)

function handleFileSelected(file: File) {
  emits('handleFileSelected', file)
}

function handleFileRejected(file: File, reason: string) {
  message.value = `文件被拒绝: ${file.name} - ${reason}`
  messageType.value = 'error'

  console.log(message.value)
  setTimeout(() => {
    message.value = ''
  }, 3000)
  emits('handleFileRejected', file)
}
</script>

<template>
  <FileSuffixUploader
    :allowed-types="['docx', 'zip']"
    class="w-full h-full border-0"
    :on-exceed="onExceed"
    @file-selected="handleFileSelected"
    @file-rejected="handleFileRejected"
  >
    <template #default>
      <motion.div
        class="relative group rounded-lg w-full h-full"
        :animate="{
          backgroundColor: isHoveredDefaultUploader
            ? 'rgba(17, 196, 162, 0.05)'
            : 'rgba(17, 196, 162, 0)',
        }"
        :transition="{ duration: 0.3, ease: 'easeOut' }"
        @mouseenter="isHoveredDefaultUploader = true"
        @mouseleave="isHoveredDefaultUploader = false"
      >
        <!-- SVG 动画边框 -->
        <!-- <motion.svg class="absolute inset-0 w-full h-full pointer-events-none">
          <motion.rect
            x="2"
            y="2"
            width="calc(100% - 4px)"
            height="calc(100% - 4px)"
            fill="none"
            stroke="#11C4A2"
            stroke-width="1"
            stroke-dasharray="4 2"
            rx="6"
            ry="6"
          />
        </motion.svg> -->

        <div
          aria-label="背景"
          class="absolute pointer-events-none inset-0 grid grid-cols-2 p-4"
        >
          <img
            :src="wordBg"
            alt="word"
            class="w-2/3 h-2/3 object-contain mx-auto"
          >
          <img
            :src="zipBg"
            alt="zip"
            class="w-2/3 h-2/3 object-contain mx-auto mt-auto"
          >
        </div>

        <motion.div
          v-if="!isHoveredDefaultUploader"
          aria-label="默认展示的样式"
          class="w-full h-full flex flex-col justify-center items-center"
          :initial="{ opacity: 0 }"
          :animate="{ opacity: 1 }"
          :exit="{ opacity: 0 }"
          :transition="{ duration: 0.3, ease: 'easeOut' }"
        >
          <div
            aria-label="按钮"
            class="w-20 h-12 bg-gradient-to-r from-[#94db76] to-[#51af90] rounded-lg flex items-center justify-center mx-auto mb-3"
          >
            <svg
              class="w-6 h-6 text-white"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 32 32"
            >
              <path
                fill="currentColor"
                d="M17 15V5h-2v10H5v2h10v10h2V17h10v-2z"
              />
            </svg>
          </div>

          <div class="text-lg font-medium text-black/85 mb-1">
            将论文拖拽到此处
          </div>
          <div class="text-sm text-black/65 font-normal">
            或直接
            <span class="text-[#59b49b] font-medium cursor-pointer">点击上传</span>
          </div>
        </motion.div>

        <motion.div
          v-if="isHoveredDefaultUploader"
          aria-label="hover展示的样式"
          class="w-full h-full flex flex-col justify-center items-center"
          :initial="{ opacity: 0, y: 10 }"
          :animate="{ opacity: 1, y: 0 }"
          :exit="{ opacity: 0, y: -10 }"
          :transition="{ duration: 0.3, ease: 'easeOut' }"
        >
          <div
            aria-label="按钮"
            class="w-32 h-12 gap-1.5 bg-gradient-to-r from-[#94db76] to-[#51af90] rounded-lg flex items-center justify-center mx-auto mb-3"
          >
            <svg
              class="w-5 h-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 32 32"
            >
              <path
                fill="currentColor"
                d="M17 15V5h-2v10H5v2h10v10h2V17h10v-2z"
              />
            </svg>

            <div class="text-white text-sm font-medium">
              选择文件
            </div>
          </div>

          <!-- <div class="text-lg font-medium text-black/85 mb-1">&nbsp;</div> -->
          <div class="text-sm text-black/65 font-normal">
            支持论文格式：Word源文件、Latex打包的Zip文件
          </div>
        </motion.div>
      </motion.div>
    </template>

    <template #docx-content>
      <div class="rounded-lg w-full h-full relative p-1 bg-[#679ff6]/5">
        <!-- SVG 动画边框 -->
        <motion.svg class="absolute inset-0 w-full h-full pointer-events-none">
          <motion.rect
            x="2"
            y="2"
            width="calc(100% - 4px)"
            height="calc(100% - 4px)"
            fill="none"
            stroke="#679ff6"
            stroke-width="1"
            stroke-dasharray="4 2"
            rx="6"
            ry="6"
            :animate="{
              strokeDashoffset: [0, 12],
            }"
            :transition="{
              duration: 2,
              repeat: Infinity,
              ease: 'linear',
            }"
          />
        </motion.svg>

        <div
          class="w-full h-full gap-2 flex flex-col justify-center items-center"
        >
          <img :src="wordIcon" alt="word" class="w-20 h-auto animate-pulse">

          <div class="text-sm text-blue-600">
            松手即可上传
          </div>
        </div>
      </div>
    </template>

    <template #zip-content>
      <div class="rounded-lg w-full h-full relative p-1 bg-[#51af90]/5">
        <!-- SVG 动画边框 -->
        <motion.svg class="absolute inset-0 w-full h-full pointer-events-none">
          <motion.rect
            x="2"
            y="2"
            width="calc(100% - 4px)"
            height="calc(100% - 4px)"
            fill="none"
            stroke="#51af90"
            stroke-width="1"
            stroke-dasharray="4 2"
            rx="6"
            ry="6"
            :animate="{
              strokeDashoffset: [0, 12],
            }"
            :transition="{
              duration: 2,
              repeat: Infinity,
              ease: 'linear',
            }"
          />
        </motion.svg>

        <div
          class="w-full h-full gap-2 flex flex-col justify-center items-center"
        >
          <img :src="zipIcon" alt="zip" class="w-20 h-auto animate-pulse">

          <div class="text-sm text-[#51af90]">
            松手即可上传
          </div>
        </div>
      </div>
    </template>

    <template #forbidden-content>
      <div class="rounded-lg w-full h-full relative p-1 bg-[#ff0000]/5">
        <!-- SVG 动画边框 -->
        <motion.svg class="absolute inset-0 w-full h-full pointer-events-none">
          <motion.rect
            x="2"
            y="2"
            width="calc(100% - 4px)"
            height="calc(100% - 4px)"
            fill="none"
            stroke="#ff0000"
            stroke-width="1"
            stroke-dasharray="4 2"
            rx="6"
            ry="6"
          />
        </motion.svg>

        <div
          class="w-full h-full gap-2 flex flex-col justify-center items-center"
        >
          <img :src="errorIcon" alt="error" class="w-20 h-auto animate-pulse">

          <div class="text-sm text-[#ff0000] font-medium">
            文件格式错误！
          </div>
          <div class="text-sm text-black/65 font-normal">
            只允许上传Word源文件或Latex打包的Zip文件
          </div>
        </div>
      </div>
    </template>
  </FileSuffixUploader>
</template>

<style scoped></style>
