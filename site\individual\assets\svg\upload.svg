<?xml version="1.0" encoding="UTF-8"?>
<svg width="80px" height="48px" viewBox="0 0 80 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>上传</title>
    <defs>
        <linearGradient x1="-1.11022302e-14%" y1="50%" x2="100%" y2="50%" id="linearGradient-1">
            <stop stop-color="#82DF67" offset="0%"></stop>
            <stop stop-color="#06B190" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="✅论文检测" transform="translate(-875.000000, -421.000000)">
            <g id="编组-3" transform="translate(140.000000, 281.000000)">
                <g id="编组" transform="translate(437.000000, 40.000000)">
                    <g id="编组-2" transform="translate(266.000000, 100.000000)">
                        <g id="上传" transform="translate(32.000000, 0.000000)">
                            <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="80" height="48" rx="8"></rect>
                            <path d="M40,16 C40.3681898,16 40.6666667,16.2984768 40.6666667,16.6666667 L40.666,23.333 L47.3333333,23.3333333 C47.7015232,23.3333333 48,23.6318102 48,24 C48,24.3681898 47.7015232,24.6666667 47.3333333,24.6666667 L40.666,24.666 L40.6666667,31.3333333 C40.6666667,31.7015232 40.3681898,32 40,32 C39.6318102,32 39.3333333,31.7015232 39.3333333,31.3333333 L39.333,24.666 L32.6666667,24.6666667 C32.2984768,24.6666667 32,24.3681898 32,24 C32,23.6318102 32.2984768,23.3333333 32.6666667,23.3333333 L39.333,23.333 L39.3333333,16.6666667 C39.3333333,16.2984768 39.6318102,16 40,16 Z" id="形状结合" stroke="#FFFFFF" fill="#FFFFFF"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>