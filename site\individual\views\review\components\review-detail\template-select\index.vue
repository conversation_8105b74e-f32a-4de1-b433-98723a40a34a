<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { onBeforeRouteLeave } from 'vue-router'
import { saveBasicInfo } from '../../../../../service/api/sys'
import { useUserStore } from '../../../../../store/modules/user'
import PaperInfo from './paper-info.vue'
import TemplateItem from './template-item.vue'

const router = useRouter()
const { setUserConfirm } = useUserStore()
const { setStatus } = inject('useStatus') as any
const STATUS = inject('STATUS') as any
const paperRef = useTemplateRef('paperRef')
const { frontTempFileInfo, setFrontTempFileInfo } = inject('useFrontTempFileInfo') as any
const selectTemplate = ref(null)
const tipMessage = {
  author: '请输入作者',
  school: '请输入学校',
  titleCn: '请输入中文标题',
  titleEN: '请输入英文标题',
  degree: '请选择学位',
}

function reUpload() {
  ElMessageBox.confirm(
    '确认重新上传？退出后当前页面内容不保留',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      setStatus(STATUS.ORIGINALSTATUS)
    })
    .catch(() => {
      // TOOD
    })
}

function selctTemplate(val) {
  selectTemplate.value = val
}

async function startCheck() {
  const paperInfo = paperRef.value?.paperInfo
  if (!paperInfo) {
    ElMessage({
      type: 'warning',
      message: '请上传文件并填写完整信息',
    })
    return
  }
   
  const validate = ['titleCn', 'titleEN', 'author', 'school', 'degree'].every((key) => {
    if (!paperInfo[key]) {
      ElMessage({
        type: 'warning',
        message: tipMessage[key],
      })
      return false
    }
    return true
  })

  if (!validate) 
    return

  if (!selectTemplate.value) {
    ElMessage({
      type: 'warning',
      message: '请选择模板',
    })
    return
  }
  // paperInfo.createTime = day(paperInfo.createTime).format('YYYY-MM-DD HH:mm:ss')
  // paperInfo.updateTime = day(paperInfo.updateTime).format('YYYY-MM-DD HH:mm:ss')

  const res = await saveBasicInfo({ 
    ...paperInfo, 
    templateUrl: 'https://paper-review.obs.cn-north-4.myhuaweicloud.com/paperTemplate/%E4%B8%AD%E5%9B%BD%E7%A7%91%E5%AD%A6%E9%99%A2%E5%A4%A7%E5%AD%A6.yaml',
  })
  frontTempFileInfo.value.reviewId = res.data.data.reviewId
  setFrontTempFileInfo(frontTempFileInfo.value)
  setStatus(STATUS.QUEUING)
}

let handle = false
onBeforeRouteLeave(async (to, from) => {
  if (handle) 
    return true
  let leave = true
  setUserConfirm(3)
  await ElMessageBox.confirm(
    '确认退出？退出后当前页面内容不保留',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      setUserConfirm(2)
    })
    .catch(() => {
      leave = false
    })
  return leave
})

function handlePopstate(e) {
  if (handle) 
    return handle = false
  e.preventDefault()
  handle = true
  const _href = window.location.href
  setTimeout(() => {
    setUserConfirm(3)
    ElMessageBox.confirm(
      '确认重新上传？退出后当前页面内容不保留',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
      .then(() => {
        setUserConfirm(2)
        window.location.href = _href
      })
      .catch(() => {
        window.location.href = '#/review'
      })
  }, 0)
}
function onBeforeunload(e) {
  e.returnValue = '确认退出？退出后当前页面内容不保留'
}
window.addEventListener('hashchange', handlePopstate)
// 浏览器关闭事件
window.addEventListener('beforeunload', onBeforeunload)

onUnmounted(() => {
  window.removeEventListener('hashchange', handlePopstate)
  window.removeEventListener('beforeunload', onBeforeunload)
})
</script>

<template>
  <div class="w-full box-border bg-[#fff] rounded-[8px] px-[40px] py-[20px] relative base:h-[643px]">
    <PaperInfo ref="paperRef" />
    <div class="border-b-[1px] border-[#e8e8e8] pb-[2.5rem] box-border">
      <div class="text-[16px] font-[600] mt-[31.5px] mb-[20px] top-0 right-0">
        选择模板
      </div>
      <div class="scrollbar-thin w-full grid grid-cols-4 cursor-pointer h-[272px] overflow-auto">
        <TemplateItem @selct-template="selctTemplate" />
      </div>
    </div>
    <div class="h-[75px] flex items-center text-[16px]">
      <el-button style="background: linear-gradient(90deg, #7ADC6A, #0FB48E);border: none; color:#fff;width: 132px;height: 48px;border-radius: 8px;font-size: 16px" @click="startCheck">
        开始检测
      </el-button>
      <el-button style="width: 132px;height: 48px;border-radius: 8px;font-size: 16px;" @click="reUpload">
        重新上传
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>