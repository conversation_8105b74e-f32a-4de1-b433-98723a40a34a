<script setup lang="ts">
import paperDescIcon from '../../../assets/svg/paper-desc.svg' 
import paperInfoIcon from '../../../assets/svg/paper-info.svg' 
import paperResIcon from '../../../assets/svg/paper-result.svg' 

withDefaults(defineProps<{
  chinese?: string
  english?: string
}>(), {
  chinese: '论文信息',
  english: 'Paper Information',
})
const iconUrlMap = {
  论文信息: paperInfoIcon,
  检测结果: paperResIcon,
  报告说明: paperDescIcon,
}
</script>

<template>
  <div 
    class="
      relative w-full text-start
      pb-2 z-10 my-6
    "
  >
    <div class="text-2xl font-medium flex flex-nowrap w-full items-center">
      <img class="w-[35px] mr-[16px]" :src="iconUrlMap[chinese] as any">
      {{ chinese }}
      <span class="text-sm ml-2 gradient-text">{{ english.toUpperCase() }}</span>
    </div>

    <div class="absolute left-[-16px] top-[-12px] font-bold text-6xl pointer-events-none z-[-1]">
      <!-- <div class="font-[Arial] gradient-text opacity-70">
        “
      </div> -->
    </div>
    
    <div class="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-[#9CE487] to-[#11C4A2]" />
  </div>
</template>

<style scoped>
.gradient-text {
  background: linear-gradient(to right, #9ce487, #11c4a2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}
</style>
