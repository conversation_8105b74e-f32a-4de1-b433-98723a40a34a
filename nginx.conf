server {
    listen 80;
    server_name localhost; # 或替换为你的域名

    location /api {
        client_max_body_size 100M;
        rewrite ^/api(.*)$ $1 break;  
        proxy_pass http://*************:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
}