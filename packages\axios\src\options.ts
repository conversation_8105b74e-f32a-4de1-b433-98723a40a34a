import type { CreateAxiosDefaults, InternalAxiosRequestConfig } from 'axios'
import type { RequestOption } from './types.ts'
import { stringify } from 'qs'

export function createDefaultOptions<ResponseData = any>(options?: Partial<RequestOption<ResponseData>>) {
  const opts: RequestOption<ResponseData> = {
    onRequest: async config => config,
    isBackendSuccess: _response => true,
    onBackendFail: async () => {},
    transformBackendResponse: async response => response.data,
    onError: async () => {},
  }

  Object.assign(opts, options)

  return opts
}

export function createAxiosConfig(config?: Partial<CreateAxiosDefaults>) {
  const TIMEOUT = 60 * 1000

  const axiosConfig: CreateAxiosDefaults = {
    timeout: TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
    paramsSerializer: (params) => {
      return stringify(params)
    },
  }

  Object.assign(axiosConfig, config)

  return axiosConfig
}

export function createDefaultHeader(config: InternalAxiosRequestConfig<any>) {
  if (config.data instanceof FormData) {
    config.headers['Content-Type'] = 'multipart/form-data'
  }
  else if (config.url && config.url.includes('download')) {
    config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
  }
  else {
    config.headers['Content-Type'] = 'application/json'
  }
}