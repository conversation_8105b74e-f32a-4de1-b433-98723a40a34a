<script setup lang="tsx">
import { ElButton, ElMessage, ElNotification } from 'element-plus'
import ReportInfo from '../../../individual/views/report/index.vue'
import { getPaperReviewNoticeApi } from '../../service/api/sys'

// TODO NOTE
// useIntervalFn(
//   handleGetPaperReviewNotice, 
//   1000 * 3, 
//   { immediateCallback: true },
// )

function handleGetPaperReviewNotice() {
  getPaperReviewNoticeApi().then((res) => {
    if (res.data.code !== 200) {
      ElMessage({
        message: res.data.message,
        type: 'error',
      })
      return
    }

    res.data?.data?.forEach((item) => {
      setTimeout(() => {
        handleAddNotification(item)
      })
    })
  })
}

function handleAddNotification(item) {
  const notificationData = {
    taskName: item.taskName,
    teacherName: item.teacherName,
    approvalStatus: item.approvalStatus === '3' ? '通过' : '不通过', // 2 不通过 3 通过
  }

  const notificationId = ElNotification({
    title: '审核结果通知',
    type: notificationData.approvalStatus === '通过' ? 'success' : 'warning',
    duration: 0,
    message: () => (
      <div class="w-full flex flex-col justify-center items-center">
        <div>
          <span>
            《
            {notificationData.taskName}
            》
          </span>
          由
          <span>{notificationData.teacherName}</span>
          确认
          <span class={cn(
            'text-[#6ED86E]',
            notificationData.approvalStatus === '不通过' && 'text-[#FF4D4F]',
          )}
          >
            {notificationData.approvalStatus}
          </span>
          {
            item.reason ? (
              <div 
                style="display: -webkit-box;
                      -webkit-box-orient: vertical;
                      -webkit-line-clamp: 2;
                      overflow: hidden;
                      text-overflow: ellipsis;"
              >
                审核理由：
                {item.reason}
              </div>
            ) : null
          }

        </div>

        <div class="w-full flex flex-row justify-center items-center gap-2 mt-3 ml-[-12px]">
          <ElButton
            type="primary"
            class={cn(
              'hover:opacity-80 !border-0 bg-gradient-to-r from-[#6ED86E] to-[#1DB989]',
            )}
            onClick={() => {
              preview(item)
              notificationId.close()
            }}
          >
            查看
          </ElButton>
              
          <ElButton onClick={() => {
            notificationId.close()
          }}
          >
            取消
          </ElButton>
        </div>
      </div>
    ),
  })
}

const reportType = ref<'statistics' | 'comparison' >('statistics')
const previewRow = ref(null)
const previewDialogVisible = ref(false)
const reportInfoRef = useTemplateRef<any>('reportInfoRef')
function preview(row) {
  previewRow.value = row
  previewDialogVisible.value = true
}

function closePreview() {
  previewRow.value = null
  previewDialogVisible.value = false
  reportInfoRef.value.clearFeedBackContent()
}
function goTop() {
  reportInfoRef.value.scrollToTop()
}

function feedback() {
  reportInfoRef.value.openFeedBackDialog()
}
</script>

<template>
  <div />
  
  <el-dialog v-model="previewDialogVisible" fullscreen :modal="false" :show-close="false">
    <div class="w-full min-w-[800px] h-[66px]  fixed top-0 bg-[#fff] border-b border-gray-200 flex items-center justify-between px-4 py-3 shadow-sm" style="z-index: 99;">
      <div class="font-medium text-gray-800">
        查看报告
      </div>

      <div class="flex items-center space-x-6">
        <button class="text-gray-800 box-border w-[80px] hover:text-teal-600 text-sm font-medium" :class="reportType === 'statistics' ? 'active' : ''" style="margin-left: 190px;" @click="reportType = 'statistics'">
          统计报告
        </button>
        <button
          class="text-gray-800  box-border w-[80px] hover:text-teal-600  text-sm font-medium"
          :class="reportType === 'comparison' ? 'active' : ''" @click="reportType = 'comparison'"
        >
          比对报告
        </button>
      </div>

      <div class="flex items-center space-x-2">
        <button class="px-3 py-1 border border-gray-200 rounded text-sm text-gray-700 hover:bg-gray-50">
          下载报告
        </button>
        <button class="px-3 py-1 border border-gray-200 rounded text-sm text-gray-700 hover:bg-gray-50" style="margin-right: 60px;">
          提交审核
        </button>
        <button class="p-1 text-gray-500 hover:text-gray-700 mt-[5x]" @click="closePreview">
          <el-icon><Close /></el-icon>
        </button>
      </div>
    </div>   

    <div class="min-h-[800px] calc-[100vh-66px] bg-gray-100 mt-[66px]">
      <ReportInfo ref="reportInfoRef" :preview-row="previewRow" />  
    </div>
    <div class="fixed bottom-0 right-0 p-4">
      <div class="icon-round mb-[16px]" @click="goTop">
        <img src="../../assets/svg/goTop.svg">
      </div>
      <!-- <div class="icon-round">
        <img src="../../assets/svg/feedBack.svg" @click="feedback">
      </div> -->
    </div>
  </el-dialog>
</template>

<style scoped>
.icon-round {
  width: 48px;
  height: 48px;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 50%;
  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
</style>